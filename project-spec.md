# Vaarta - AI Chat Application Technical Specification

## Overview

Vaarta is a cross-platform AI chat application built with Flutter that aggregates multiple AI providers and supports Model Context Protocol (MCP) tool integration. The application follows Material 3 design principles and enables users to organize conversations in workspaces while accessing external tools and data sources.

## Core Architecture

### Application Structure
```
Vaarta/
├── Chat Management
│   ├── Spaces (Workspaces)
│   ├── Threads (Conversations)
│   └── Messages
├── AI Provider Integration
│   ├── OpenRouter API
│   ├── Direct Provider APIs
│   └── Model Management
├── MCP Integration
│   ├── Local MCP Servers (STDIO)
│   ├── Remote MCP Servers (SSE)
│   └── Tool Execution
├── Persona System
│   ├── Built-in Personas
│   ├── Custom Personas
│   └── Context Awareness
└── Data Persistence
    ├── Local Storage (Hive)
    ├── Settings Management
    └── Conversation History
```

### Technology Stack
- **Framework:** Flutter with Material 3
- **State Management:** flutter_bloc
- **Local Storage:** Hive
- **HTTP Client:** dio
- **Markdown Rendering:** gpt_markdown
- **MCP Integration:** mcp_client
- **Voice Input:** speech_to_text
- **Process Management:** process_run

## User Interface Design

### Material 3 Implementation
- **Color System:** Dynamic Material You theming with user customization
- **Typography:** Material 3 type scale with adjustable font sizes
- **Navigation:** Bottom navigation with floating action button
- **Components:** Material 3 cards, buttons, and input fields

### Screen Hierarchy
```
Bottom Navigation
├── Spaces (Home)
├── Chat Interface  
├── Tools
└── Settings

Modal Screens
├── Space Creation
├── Persona Selection
├── Tool Configuration
└── Research Output
```

### Spaces Overview Screen
- **Header:** App title with settings access
- **Content:** Grid of workspace cards with custom icons
- **Empty State:** Illustrated onboarding with "Create a space" prompt
- **FAB:** Floating action button for new space creation

### Chat Interface Screen
- **App Bar:** 
  - Back navigation to spaces
  - Model selector dropdown (centered)
  - Persona indicator (right)
  - New chat action
- **Message List:**
  - User messages: Right-aligned with user avatar
  - AI messages: Left-aligned with persona avatar and model attribution
  - Thinking process: Expandable cards showing reasoning steps
  - Tool execution: Progress indicators with result embedding
- **Input Area:**
  - Text field with voice input capability
  - Send button with loading states
  - Tool suggestions when appropriate

### Space Creation Flow
- **Title Input:** Text field with validation
- **Icon Selection:** Grid of emoji options with search
- **Description:** Optional text area
- **AI Prompt:** Optional system message configuration
- **Privacy Settings:** Simple toggle (private by default)

## Data Models

### Core Entities
```dart
class Space {
  final String id;
  final String title;
  final String? description;
  final String? icon;
  final String? systemPrompt;
  final DateTime created;
  final List<String> threadIds;
  final String? defaultPersonaId;
}

class Thread {
  final String id;
  final String spaceId;
  final String title;
  final DateTime created;
  final List<Message> messages;
  final String modelId;
  final String? personaId;
}

class Message {
  final String id;
  final String content;
  final bool isUser;
  final DateTime timestamp;
  final String? modelUsed;
  final String? personaUsed;
  final ThinkingProcess? reasoning;
  final List<ToolExecution>? toolResults;
}

class AIPersona {
  final String id;
  final String name;
  final String description;
  final String systemPrompt;
  final String avatar; // emoji
  final List<String> specialties;
  final bool isBuiltIn;
}

class AIModel {
  final String id;
  final String name;
  final String provider;
  final bool supportsStreaming;
  final bool supportsTools;
}
```

### MCP Integration Models
```dart
class McpServer {
  final String id;
  final String name;
  final String url; // for remote servers
  final String? command; // for local servers
  final List<String>? args;
  final ServerType type; // Local or Remote
  final bool isConnected;
}

class McpTool {
  final String id;
  final String name;
  final String description;
  final Map<String, dynamic> parameters;
  final String serverId;
}

class ToolExecution {
  final String toolId;
  final Map<String, dynamic> input;
  final String? result;
  final DateTime timestamp;
  final ExecutionStatus status;
}
```

## AI Provider Integration

### OpenRouter Implementation
- **Primary Integration:** OpenRouter API for model aggregation
- **Supported Models:** GPT-4o, Claude 3.5 Sonnet, Gemini Pro, and others
- **Streaming Support:** Server-sent events for real-time responses
- **Tool Calling:** Function calling for MCP tool integration

### Model Management
- **Dynamic Discovery:** Fetch available models from OpenRouter
- **Model Metadata:** Display capabilities, context limits, and pricing
- **Switching:** Allow model changes mid-conversation
- **Favorites:** User-selected preferred models

### Request Handling
```
User Input → Persona System Prompt + User Message → 
AI Provider API → Streaming Response → 
Tool Calls (if any) → MCP Execution → 
Final Response → UI Display
```

## MCP (Model Context Protocol) Integration

### Server Types
- **Local Servers:** STDIO-based subprocess execution
- **Remote Servers:** SSE-based HTTP connections
- **Configuration:** Simple name + URL for remote, command + args for local

### Tool Discovery Flow
```
App Startup → Connect to Configured MCP Servers → 
List Available Tools → Cache Tool Metadata → 
Present Tools in Chat Context → Execute on AI Request
```

### Example Integrations
- **Web Search:** Real-time internet search capability
- **File System:** Local file operations and document processing
- **Git Operations:** Repository management and code analysis
- **Documentation:** Access to technical documentation and code bases

## Persona System

### Built-in Personas
- **General Assistant:** Balanced, helpful responses
- **Research Analyst:** Deep analysis with web search integration
- **Code Expert:** Programming-focused with development tools
- **Creative Writer:** Storytelling and content generation
- **Data Scientist:** Analytics with visualization tools

### Custom Persona Creation
- **Name and Avatar:** User-defined identification
- **System Prompt:** Custom behavior instructions
- **Tool Preferences:** Preferred MCP tools for the persona
- **Specialties:** Categorical tags for context-aware suggestions

### Context-Aware Selection
- **Automatic Suggestions:** Recommend personas based on conversation content
- **Quick Switching:** Dropdown selector in chat header
- **Space Defaults:** Default persona per workspace

## Message Processing Pipeline

### Input Processing
```
User Input → Voice-to-Text (if applicable) → 
Text Validation → Persona Context Application → 
Model API Request Formation
```

### Response Generation
```
API Response Stream → Markdown Parsing → 
Tool Call Detection → MCP Execution (if needed) → 
UI Update with Streaming Display
```

### Thinking Process Display
- **Sequential Steps:** Numbered reasoning stages
- **Expandable Cards:** Collapsible detailed analysis
- **Tool Invocation:** Visual indicators for external tool usage
- **Progress Tracking:** Real-time execution status

## Local Storage Architecture

### Hive Boxes Structure
```dart
// Core conversation data
Box<Space> spaces;
Box<Thread> threads;
Box<Message> messages;

// AI configuration
Box<AIPersona> personas;
Box<AIModel> models;

// MCP configuration
Box<McpServer> mcpServers;
Box<McpTool> mcpTools;

// Application settings
Box<dynamic> settings;
```

### Data Persistence Strategy
- **Immediate Persistence:** Save messages as they arrive
- **Conversation Limits:** Automatic cleanup of old conversations
- **Export Capability:** JSON export for backup purposes
- **Settings Sync:** Preserve user customizations across sessions

## Research Mode Implementation

### Sequential Thinking Framework
- **Multi-step Analysis:** Break complex queries into stages
- **Branching Logic:** Allow alternative reasoning paths
- **Tool Coordination:** Orchestrate multiple MCP tools
- **Progress Visualization:** Real-time thinking process display

### Research Workflow
```
Query Analysis → Planning → Information Gathering → 
Source Validation → Synthesis → Report Generation
```

### Implementation Pattern
```dart
class ResearchSession {
  final String query;
  final List<ThinkingStep> steps;
  final List<ToolExecution> toolCalls;
  final ResearchReport? finalReport;
  
  Future<void> executeNextStep() {
    // Coordinate between thinking and tool execution
  }
}
```

## Settings and Configuration

### User Preferences
- **Appearance:** Theme selection, font size adjustment
- **Behavior:** Default models, persona preferences
- **Privacy:** Conversation retention settings
- **Voice:** Input language and recognition settings

### MCP Server Configuration
- **Server Management:** Add/remove/configure MCP servers
- **Tool Selection:** Enable/disable specific tools
- **Connection Status:** Visual indicators for server health

### Backup and Restore
- **Settings Export:** JSON configuration file
- **Conversation Export:** Structured data format
- **Import Capability:** Restore from exported files

## Security and Privacy

### Data Protection
- **Local-First Architecture:** All data stored locally by default
- **API Key Security:** Secure storage using flutter_secure_storage
- **Optional Cloud Sync:** End-to-end encrypted synchronization

### MCP Security
- **Sandboxed Execution:** Isolated tool execution environment
- **Permission Management:** User approval for sensitive operations
- **Audit Logging:** Track tool usage and data access

## Performance Considerations

### Optimization Strategies
- **Lazy Loading:** Load conversations and messages on demand
- **Message Pagination:** Limit initial message load per thread
- **Image Caching:** Efficient handling of generated images
- **Background Processing:** Tool execution without blocking UI

### Memory Management
- **Conversation Limits:** Automatic pruning of old messages
- **Cache Management:** Clear tool result caches periodically
- **Resource Monitoring:** Track and limit resource usage

## Cross-Platform Compatibility

### Flutter Implementation
- **Responsive Design:** Adaptive layouts for different screen sizes
- **Platform Integration:** Native file pickers and system integration
- **Material 3:** Consistent design across Android and other platforms

### Platform-Specific Features
- **Android:** Material You dynamic theming, notification integration
- **Desktop:** Window management, keyboard shortcuts
- **File System Access:** Platform-appropriate file operations

This specification provides a comprehensive technical foundation for building Vaarta as a focused, capable AI chat application that leverages proven patterns from successful applications while introducing MCP integration as a key differentiator.

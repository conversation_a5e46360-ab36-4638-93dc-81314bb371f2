import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'data/hive_storage.dart';
import 'data/repositories/repositories.dart';
import 'blocs/spaces/spaces_cubit.dart';
import 'blocs/threads/threads_cubit.dart';
import 'blocs/personas/personas_cubit.dart';
import 'blocs/settings/settings_cubit.dart';
import 'blocs/mcp_tools/mcp_tools_cubit.dart';
import 'screens/home/<USER>';
import 'screens/settings/settings_screen.dart';
import 'theme/app_theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Hive storage
  await HiveStorage.initialize();

  runApp(const VaartaApp());
}

class VaartaApp extends StatelessWidget {
  const VaartaApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiRepositoryProvider(
      providers: [
        RepositoryProvider(create: (context) => SpaceRepository()),
        RepositoryProvider(create: (context) => ThreadRepository()),
        RepositoryProvider(create: (context) => PersonaRepository()),
      ],
      child: MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => SpacesCubit(
              spaceRepository: context.read<SpaceRepository>(),
              threadRepository: context.read<ThreadRepository>(),
            ),
          ),
          BlocProvider(
            create: (context) => ThreadsCubit(
              threadRepository: context.read<ThreadRepository>(),
              spaceRepository: context.read<SpaceRepository>(),
            ),
          ),
          BlocProvider(
            create: (context) => PersonasCubit(
              personaRepository: context.read<PersonaRepository>(),
            )..loadPersonas(),
          ),
          BlocProvider(create: (context) => SettingsCubit()..loadSettings()),
          BlocProvider(create: (context) => MCPToolsCubit()),
        ],
        child: MaterialApp(
          title: 'Vaarta',
          debugShowCheckedModeBanner: false,
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: ThemeMode.system,
          home: const HomeScreen(),
          routes: {'/settings': (context) => const SettingsScreen()},
        ),
      ),
    );
  }
}

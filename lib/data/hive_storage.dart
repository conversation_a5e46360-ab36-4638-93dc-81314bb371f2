import 'package:hive_flutter/hive_flutter.dart';
import '../models/models.dart';

class HiveStorage {
  static const String spacesBoxName = 'spaces';
  static const String threadsBoxName = 'threads';
  static const String messagesBoxName = 'messages';
  static const String personasBoxName = 'personas';
  static const String modelsBoxName = 'models';
  static const String mcpServersBoxName = 'mcp_servers';
  static const String mcpToolsBoxName = 'mcp_tools';
  static const String settingsBoxName = 'settings';
  static const String aiProvidersBoxName = 'ai_providers';

  static Future<void> initialize() async {
    // Initialize Hive
    await Hive.initFlutter();

    // Register adapters for all our models
    Hive.registerAdapter(SpaceAdapter());
    Hive.registerAdapter(ThreadAdapter());
    Hive.registerAdapter(MessageAdapter());
    Hive.registerAdapter(ThinkingProcessAdapter());
    Hive.registerAdapter(ThinkingStepAdapter());
    Hive.registerAdapter(ToolExecutionAdapter());
    Hive.registerAdapter(ExecutionStatusAdapter());
    Hive.registerAdapter(AIPersonaAdapter());
    Hive.registerAdapter(AIModelAdapter());
    Hive.registerAdapter(McpToolAdapter());
    Hive.registerAdapter(AIProviderAdapter());
    Hive.registerAdapter(MCPServerAdapter());
    Hive.registerAdapter(MCPTransportAdapter());
    Hive.registerAdapter(MCPTransportTypeAdapter());

    // Open all boxes
    await Future.wait([
      Hive.openBox<Space>(spacesBoxName),
      Hive.openBox<Thread>(threadsBoxName),
      Hive.openBox<Message>(messagesBoxName),
      Hive.openBox<AIPersona>(personasBoxName),
      Hive.openBox<AIModel>(modelsBoxName),
      Hive.openBox<MCPServer>(mcpServersBoxName),
      Hive.openBox<McpTool>(mcpToolsBoxName),
      Hive.openBox(settingsBoxName),
      Hive.openBox<AIProvider>(aiProvidersBoxName),
    ]);

    // Initialize with default data if needed
    await _initializeDefaultData();
  }

  static Future<void> _initializeDefaultData() async {
    final personasBox = Hive.box<AIPersona>(personasBoxName);
    
    // Add default personas if box is empty
    if (personasBox.isEmpty) {
      final defaultPersonas = [
        AIPersona(
          id: 'general-assistant',
          name: 'General Assistant',
          description: 'Balanced, helpful responses for everyday tasks',
          systemPrompt: 'You are a helpful, knowledgeable assistant. Provide clear, accurate, and balanced responses to user queries.',
          avatar: '🤖',
          specialties: ['general', 'helpful', 'balanced'],
          isBuiltIn: true,
        ),
        AIPersona(
          id: 'research-analyst',
          name: 'Research Analyst',
          description: 'Deep analysis with web search integration',
          systemPrompt: 'You are a research analyst. Provide thorough, well-researched responses with citations and sources. Use available tools to gather current information.',
          avatar: '🔍',
          specialties: ['research', 'analysis', 'web-search'],
          isBuiltIn: true,
        ),
        AIPersona(
          id: 'code-expert',
          name: 'Code Expert',
          description: 'Programming-focused with development tools',
          systemPrompt: 'You are a coding expert. Provide clear, well-commented code examples and technical guidance. Use development tools when available.',
          avatar: '💻',
          specialties: ['programming', 'development', 'technical'],
          isBuiltIn: true,
        ),
        AIPersona(
          id: 'creative-writer',
          name: 'Creative Writer',
          description: 'Storytelling and content generation',
          systemPrompt: 'You are a creative writer. Help with storytelling, content creation, and creative writing tasks. Be imaginative and engaging.',
          avatar: '✍️',
          specialties: ['writing', 'creativity', 'storytelling'],
          isBuiltIn: true,
        ),
        AIPersona(
          id: 'data-scientist',
          name: 'Data Scientist',
          description: 'Analytics with visualization tools',
          systemPrompt: 'You are a data scientist. Help with data analysis, statistics, and visualization. Use analytical tools when available.',
          avatar: '📊',
          specialties: ['data', 'analytics', 'visualization'],
          isBuiltIn: true,
        ),
      ];

      for (final persona in defaultPersonas) {
        await personasBox.put(persona.id, persona);
      }
    }
  }

  // Boxes getters
  static Box<Space> get spacesBox => Hive.box<Space>(spacesBoxName);
  static Box<Thread> get threadsBox => Hive.box<Thread>(threadsBoxName);
  static Box<Message> get messagesBox => Hive.box<Message>(messagesBoxName);
  static Box<AIPersona> get personasBox => Hive.box<AIPersona>(personasBoxName);
  static Box<AIModel> get modelsBox => Hive.box<AIModel>(modelsBoxName);
  static Box<MCPServer> get mcpServersBox => Hive.box<MCPServer>(mcpServersBoxName);
  static Box<McpTool> get mcpToolsBox => Hive.box<McpTool>(mcpToolsBoxName);
  static Box get settingsBox => Hive.box(settingsBoxName);
  static Box<AIProvider> get aiProvidersBox => Hive.box<AIProvider>(aiProvidersBoxName);

  // Settings box helper
  static Future<Box> getSettingsBox() async {
    return Hive.isBoxOpen(settingsBoxName) 
        ? Hive.box(settingsBoxName)
        : await Hive.openBox(settingsBoxName);
  }

  static Future<void> close() async {
    await Hive.close();
  }

  static Future<void> clear() async {
    await spacesBox.clear();
    await threadsBox.clear();
    await messagesBox.clear();
    await personasBox.clear();
    await modelsBox.clear();
    await mcpServersBox.clear();
    await mcpToolsBox.clear();
    await settingsBox.clear();
    await aiProvidersBox.clear();
  }

  // AI Provider methods
  static Future<Map<String, AIProvider>> getAIProviders() async {
    final providers = <String, AIProvider>{};
    for (final provider in aiProvidersBox.values) {
      providers[provider.id] = provider;
    }
    return providers;
  }

  static Future<void> saveAIProvider(AIProvider provider) async {
    await aiProvidersBox.put(provider.id, provider);
  }

  static Future<void> deleteAIProvider(String providerId) async {
    await aiProvidersBox.delete(providerId);
  }

  // MCP Server methods
  static Future<Map<String, MCPServer>> getMCPServers() async {
    final servers = <String, MCPServer>{};
    for (final server in mcpServersBox.values.cast<MCPServer>()) {
      servers[server.id] = server;
    }
    return servers;
  }

  static Future<void> saveMCPServer(MCPServer server) async {
    await mcpServersBox.put(server.id, server);
  }

  static Future<void> deleteMCPServer(String serverId) async {
    await mcpServersBox.delete(serverId);
  }
}
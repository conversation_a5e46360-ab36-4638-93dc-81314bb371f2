import 'package:hive/hive.dart';
import '../../models/models.dart';
import '../hive_storage.dart';

class SpaceRepository {
  Box<Space> get _box => HiveStorage.spacesBox;

  Future<List<Space>> getAllSpaces() async {
    return _box.values.toList()..sort((a, b) => b.created.compareTo(a.created));
  }

  Future<Space?> getSpace(String id) async {
    return _box.get(id);
  }

  Future<void> saveSpace(Space space) async {
    await _box.put(space.id, space);
  }

  Future<void> deleteSpace(String id) async {
    await _box.delete(id);
  }

  Future<void> updateSpace(Space space) async {
    await _box.put(space.id, space);
  }

  Stream<BoxEvent> watchSpaces() {
    return _box.watch();
  }

  Future<Space> createSpace({
    required String title,
    String? description,
    String? icon,
    String? systemPrompt,
    String? defaultPersonaId,
  }) async {
    final space = Space(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      description: description,
      icon: icon ?? '💬',
      systemPrompt: systemPrompt,
      created: DateTime.now(),
      threadIds: [],
      defaultPersonaId: defaultPersonaId,
    );
    await saveSpace(space);
    return space;
  }

  Future<Space> addThreadToSpace(String spaceId, String threadId) async {
    final space = await getSpace(spaceId);
    if (space == null) throw Exception('Space not found: $spaceId');
    
    final updatedSpace = space.copyWith(
      threadIds: [...space.threadIds, threadId],
    );
    await updateSpace(updatedSpace);
    return updatedSpace;
  }

  Future<Space> removeThreadFromSpace(String spaceId, String threadId) async {
    final space = await getSpace(spaceId);
    if (space == null) throw Exception('Space not found: $spaceId');
    
    final updatedSpace = space.copyWith(
      threadIds: space.threadIds.where((id) => id != threadId).toList(),
    );
    await updateSpace(updatedSpace);
    return updatedSpace;
  }
}
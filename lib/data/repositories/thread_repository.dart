import 'package:hive/hive.dart';
import '../../models/models.dart';
import '../hive_storage.dart';

class ThreadRepository {
  Box<Thread> get _box => HiveStorage.threadsBox;

  Future<List<Thread>> getAllThreads() async {
    return _box.values.toList()..sort((a, b) => b.created.compareTo(a.created));
  }

  Future<List<Thread>> getThreadsForSpace(String spaceId) async {
    return _box.values
        .where((thread) => thread.spaceId == spaceId)
        .toList()
      ..sort((a, b) => b.created.compareTo(a.created));
  }

  Future<Thread?> getThread(String id) async {
    return _box.get(id);
  }

  Future<void> saveThread(Thread thread) async {
    await _box.put(thread.id, thread);
  }

  Future<void> deleteThread(String id) async {
    await _box.delete(id);
  }

  Future<void> updateThread(Thread thread) async {
    await _box.put(thread.id, thread);
  }

  Stream<BoxEvent> watchThreads() {
    return _box.watch();
  }

  Future<Thread> createThread({
    required String spaceId,
    required String title,
    required String modelId,
    String? personaId,
  }) async {
    final thread = Thread(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      spaceId: spaceId,
      title: title,
      created: DateTime.now(),
      messages: [],
      modelId: modelId,
      personaId: personaId,
    );
    await saveThread(thread);
    return thread;
  }

  Future<Thread> addMessageToThread(String threadId, Message message) async {
    final thread = await getThread(threadId);
    if (thread == null) throw Exception('Thread not found: $threadId');
    
    final updatedThread = thread.copyWith(
      messages: [...thread.messages, message],
    );
    await updateThread(updatedThread);
    return updatedThread;
  }

  Future<Thread> updateThreadModel(String threadId, String modelId) async {
    final thread = await getThread(threadId);
    if (thread == null) throw Exception('Thread not found: $threadId');
    
    final updatedThread = thread.copyWith(modelId: modelId);
    await updateThread(updatedThread);
    return updatedThread;
  }

  Future<Thread> updateThreadPersona(String threadId, String? personaId) async {
    final thread = await getThread(threadId);
    if (thread == null) throw Exception('Thread not found: $threadId');
    
    final updatedThread = thread.copyWith(personaId: personaId);
    await updateThread(updatedThread);
    return updatedThread;
  }

  Future<void> deleteThreadsForSpace(String spaceId) async {
    final threadsToDelete = await getThreadsForSpace(spaceId);
    for (final thread in threadsToDelete) {
      await deleteThread(thread.id);
    }
  }
}
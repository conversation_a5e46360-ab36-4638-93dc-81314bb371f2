import 'package:hive/hive.dart';
import '../../models/models.dart';
import '../hive_storage.dart';

class PersonaRepository {
  Box<AIPersona> get _box => HiveStorage.personasBox;

  Future<List<AIPersona>> getAllPersonas() async {
    return _box.values.toList()..sort((a, b) {
      // Built-in personas first, then alphabetical
      if (a.isBuiltIn && !b.isBuiltIn) return -1;
      if (!a.isBuiltIn && b.isBuiltIn) return 1;
      return a.name.compareTo(b.name);
    });
  }

  Future<List<AIPersona>> getBuiltInPersonas() async {
    return _box.values.where((persona) => persona.isBuiltIn).toList()
      ..sort((a, b) => a.name.compareTo(b.name));
  }

  Future<List<AIPersona>> getCustomPersonas() async {
    return _box.values.where((persona) => !persona.isBuiltIn).toList()
      ..sort((a, b) => a.name.compareTo(b.name));
  }

  Future<AIPersona?> getPersona(String id) async {
    return _box.get(id);
  }

  Future<AIPersona?> getDefaultPersona() async {
    // Return the general assistant as default
    return _box.get('general-assistant');
  }

  Future<List<AIPersona>> getPersonasBySpecialty(String specialty) async {
    return _box.values
        .where((persona) => persona.specialties.contains(specialty.toLowerCase()))
        .toList()
      ..sort((a, b) => a.name.compareTo(b.name));
  }

  Future<void> savePersona(AIPersona persona) async {
    await _box.put(persona.id, persona);
  }

  Future<void> deletePersona(String id) async {
    final persona = await getPersona(id);
    if (persona?.isBuiltIn == true) {
      throw Exception('Cannot delete built-in persona');
    }
    await _box.delete(id);
  }

  Future<void> updatePersona(AIPersona persona) async {
    if (persona.isBuiltIn) {
      throw Exception('Cannot update built-in persona');
    }
    await _box.put(persona.id, persona);
  }

  Stream<BoxEvent> watchPersonas() {
    return _box.watch();
  }

  Future<AIPersona> createCustomPersona({
    required String name,
    required String description,
    required String systemPrompt,
    required String avatar,
    required List<String> specialties,
  }) async {
    final persona = AIPersona(
      id: 'custom-${DateTime.now().millisecondsSinceEpoch}',
      name: name,
      description: description,
      systemPrompt: systemPrompt,
      avatar: avatar,
      specialties: specialties.map((s) => s.toLowerCase()).toList(),
      isBuiltIn: false,
    );
    await savePersona(persona);
    return persona;
  }

  Future<List<AIPersona>> suggestPersonasForContent(String content) async {
    final allPersonas = await getAllPersonas();
    final contentLower = content.toLowerCase();
    
    // Score personas based on specialty matches
    final scoredPersonas = <MapEntry<AIPersona, int>>[];
    
    for (final persona in allPersonas) {
      int score = 0;
      
      for (final specialty in persona.specialties) {
        if (contentLower.contains(specialty)) {
          score += 2; // Direct specialty match
        }
      }
      
      // Check for keyword matches in description
      if (persona.description.toLowerCase().split(' ').any((word) => 
          contentLower.contains(word) && word.length > 3)) {
        score += 1;
      }
      
      if (score > 0) {
        scoredPersonas.add(MapEntry(persona, score));
      }
    }
    
    // Sort by score and return top suggestions
    scoredPersonas.sort((a, b) => b.value.compareTo(a.value));
    return scoredPersonas.take(3).map((entry) => entry.key).toList();
  }
}

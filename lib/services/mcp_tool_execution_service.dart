import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../models/models.dart';
import 'mcp_connection_manager.dart';

/// Service for executing MCP tools and handling results
class MCPToolExecutionService {
  static final MCPToolExecutionService _instance =
      MCPToolExecutionService._internal();
  factory MCPToolExecutionService() => _instance;
  MCPToolExecutionService._internal();

  final MCPConnectionManager _connectionManager = MCPConnectionManager();

  /// Execute a tool by its ID with the given arguments
  Future<ToolExecution> executeTool({
    required String toolId,
    required Map<String, dynamic> arguments,
  }) async {
    final startTime = DateTime.now();

    try {
      // Parse tool ID to get server ID and tool name
      final parts = toolId.split(':');
      if (parts.length != 2) {
        throw Exception('Invalid tool ID format: $toolId');
      }

      final serverId = parts[0];
      final toolName = parts[1];

      // Check if server is connected
      if (!_connectionManager.isConnected(serverId)) {
        throw Exception('Server $serverId is not connected');
      }

      // Get client for the server
      final client = _connectionManager.getClient(serverId);
      if (client == null) {
        throw Exception('No client available for server $serverId');
      }

      // Execute the tool
      final result = await client.callTool(toolName, arguments: arguments);

      return ToolExecution(
        toolId: toolId,
        input: arguments,
        result: jsonEncode(result),
        timestamp: startTime,
        status: ExecutionStatus.completed,
      );
    } catch (e) {
      return ToolExecution(
        toolId: toolId,
        input: arguments,
        result: null,
        timestamp: startTime,
        status: ExecutionStatus.failed,
        errorMessage: e.toString(),
      );
    }
  }

  /// Execute multiple tools in sequence
  Future<List<ToolExecution>> executeTools(List<ToolCall> toolCalls) async {
    final results = <ToolExecution>[];

    for (final toolCall in toolCalls) {
      final execution = await executeTool(
        toolId: toolCall.toolId,
        arguments: toolCall.arguments,
      );
      results.add(execution);
    }

    return results;
  }

  /// Parse tool calls from AI response
  List<ToolCall> parseToolCallsFromResponse(String response) {
    final toolCalls = <ToolCall>[];

    try {
      // Try to parse as JSON first (for structured tool calls)
      final json = jsonDecode(response);
      if (json is Map<String, dynamic> && json.containsKey('tool_calls')) {
        final calls = json['tool_calls'] as List;
        for (final call in calls) {
          if (call is Map<String, dynamic>) {
            toolCalls.add(ToolCall.fromJson(call));
          }
        }
      }
    } catch (e) {
      // If JSON parsing fails, try to extract tool calls from text
      toolCalls.addAll(_parseToolCallsFromText(response));
    }

    return toolCalls;
  }

  /// Parse tool calls from text response (fallback method)
  List<ToolCall> _parseToolCallsFromText(String text) {
    final toolCalls = <ToolCall>[];

    // Simple pattern matching for now - can be enhanced later
    if (text.contains('function_call') || text.contains('tool_call')) {
      // For now, return empty list - this can be enhanced with proper parsing
      debugPrint('Tool calls detected in text but parsing not implemented yet');
    }

    return toolCalls;
  }

  /// Format tool execution result for display
  String formatToolResult(ToolExecution execution) {
    final buffer = StringBuffer();

    // Extract tool name from toolId (format: serverId:toolName)
    final toolName = execution.toolId.contains(':')
        ? execution.toolId.split(':').last
        : execution.toolId;

    buffer.writeln('🔧 **$toolName**');
    buffer.writeln('Status: ${execution.status.name}');
    buffer.writeln('Timestamp: ${execution.timestamp}');

    if (execution.status == ExecutionStatus.completed) {
      buffer.writeln('\n**Result:**');
      if (execution.result != null) {
        buffer.writeln('```');
        buffer.writeln(execution.result);
        buffer.writeln('```');
      } else {
        buffer.writeln('No result returned');
      }
    } else if (execution.status == ExecutionStatus.failed) {
      buffer.writeln('\n**Error:**');
      buffer.writeln(execution.errorMessage ?? 'Unknown error');
    }

    return buffer.toString();
  }

  /// Format multiple tool execution results
  String formatToolResults(List<ToolExecution> executions) {
    if (executions.isEmpty) return '';

    final buffer = StringBuffer();
    buffer.writeln('## Tool Execution Results\n');

    for (int i = 0; i < executions.length; i++) {
      if (i > 0) buffer.writeln('\n---\n');
      buffer.writeln(formatToolResult(executions[i]));
    }

    return buffer.toString();
  }

  /// Check if a response contains tool calls
  bool containsToolCalls(String response) {
    // Check for JSON tool calls
    try {
      final json = jsonDecode(response);
      if (json is Map<String, dynamic> && json.containsKey('tool_calls')) {
        return true;
      }
    } catch (e) {
      // Ignore JSON parsing errors
    }

    // Check for text-based tool calls
    return response.contains('<tool_call') ||
        response.contains('tool_calls') ||
        response.contains('function_call');
  }
}

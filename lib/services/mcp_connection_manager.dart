import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/models.dart';
import 'mcp_client.dart' as mcp;

/// Manages multiple MCP server connections
class MCPConnectionManager {
  static final MCPConnectionManager _instance = MCPConnectionManager._internal();
  factory MCPConnectionManager() => _instance;
  MCPConnectionManager._internal();

  final Map<String, mcp.MCPClient> _connections = {};
  final Map<String, MCPServer> _serverConfigs = {};
  final StreamController<MCPConnectionEvent> _eventController = 
      StreamController<MCPConnectionEvent>.broadcast();

  Stream<MCPConnectionEvent> get events => _eventController.stream;

  /// Get all configured servers
  List<MCPServer> get servers => _serverConfigs.values.toList();

  /// Get all active connections
  List<String> get activeConnections => _connections.keys.toList();

  /// Add or update a server configuration
  Future<void> addServer(MCPServer server) async {
    _serverConfigs[server.id] = server;
    _eventController.add(MCPConnectionEvent.serverAdded(server));
    
    // Auto-connect if enabled
    if (server.autoConnect) {
      await connectToServer(server.id);
    }
  }

  /// Remove a server configuration
  Future<void> removeServer(String serverId) async {
    final server = _serverConfigs.remove(serverId);
    if (server != null) {
      await disconnectFromServer(serverId);
      _eventController.add(MCPConnectionEvent.serverRemoved(server));
    }
  }

  /// Connect to a specific server
  Future<void> connectToServer(String serverId) async {
    final server = _serverConfigs[serverId];
    if (server == null) {
      throw mcp.MCPException('Server not found: $serverId');
    }

    if (_connections.containsKey(serverId)) {
      // Already connected
      return;
    }

    try {
      _eventController.add(MCPConnectionEvent.connecting(server));
      
      mcp.MCPTransport transport;
      
      switch (server.transport.type) {
        case MCPTransportType.http:
          transport = mcp.MCPHttpTransport(server.transport.url!);
          break;
        case MCPTransportType.stdio:
          final processTransport = mcp.MCPProcessTransport(
            command: server.transport.command!,
            args: server.transport.args ?? [],
          );
          await processTransport.start();
          transport = processTransport;
          break;
      }

      final client = mcp.MCPClient(transport);
      
      // Initialize connection
      final initResult = await client.initialize(
        clientName: 'Vaarta',
        clientVersion: '1.0.0',
        clientCapabilities: {
          'sampling': true,
          'roots': {
            'listChanged': true,
          },
        },
      );

      _connections[serverId] = client;
      
      // Listen for notifications
      client.notifications.listen((notification) {
        _eventController.add(MCPConnectionEvent.notificationReceived(
          server, 
          notification,
        ));
      });

      _eventController.add(MCPConnectionEvent.connected(server, initResult));
      
    } catch (e) {
      _eventController.add(MCPConnectionEvent.connectionFailed(server, e.toString()));
      throw mcp.MCPException('Failed to connect to $serverId: $e');
    }
  }

  /// Disconnect from a specific server
  Future<void> disconnectFromServer(String serverId) async {
    final client = _connections.remove(serverId);
    final server = _serverConfigs[serverId];
    
    if (client != null) {
      await client.close();
      if (server != null) {
        _eventController.add(MCPConnectionEvent.disconnected(server));
      }
    }
  }

  /// Get client for a specific server
  mcp.MCPClient? getClient(String serverId) {
    return _connections[serverId];
  }

  /// Check if connected to a server
  bool isConnected(String serverId) {
    return _connections.containsKey(serverId);
  }

  /// Get all available tools from all connected servers
  Future<Map<String, List<mcp.MCPTool>>> getAllTools() async {
    final result = <String, List<mcp.MCPTool>>{};
    
    for (final entry in _connections.entries) {
      try {
        final tools = await entry.value.listTools();
        result[entry.key] = tools;
      } catch (e) {
        debugPrint('Failed to get tools from ${entry.key}: $e');
        result[entry.key] = [];
      }
    }
    
    return result;
  }

  /// Get all available resources from all connected servers
  Future<Map<String, List<mcp.MCPResource>>> getAllResources() async {
    final result = <String, List<mcp.MCPResource>>{};
    
    for (final entry in _connections.entries) {
      try {
        final resources = await entry.value.listResources();
        result[entry.key] = resources;
      } catch (e) {
        debugPrint('Failed to get resources from ${entry.key}: $e');
        result[entry.key] = [];
      }
    }
    
    return result;
  }

  /// Get all available prompts from all connected servers
  Future<Map<String, List<mcp.MCPPrompt>>> getAllPrompts() async {
    final result = <String, List<mcp.MCPPrompt>>{};
    
    for (final entry in _connections.entries) {
      try {
        final prompts = await entry.value.listPrompts();
        result[entry.key] = prompts;
      } catch (e) {
        debugPrint('Failed to get prompts from ${entry.key}: $e');
        result[entry.key] = [];
      }
    }
    
    return result;
  }

  /// Execute a tool on a specific server
  Future<Map<String, dynamic>> executeTool({
    required String serverId,
    required String toolName,
    Map<String, dynamic>? arguments,
  }) async {
    final client = _connections[serverId];
    if (client == null) {
      throw mcp.MCPException('Not connected to server: $serverId');
    }

    final server = _serverConfigs[serverId]!;
    _eventController.add(MCPConnectionEvent.toolExecuting(server, toolName));

    try {
      final result = await client.callTool(toolName, arguments: arguments);
      _eventController.add(MCPConnectionEvent.toolExecuted(server, toolName, result));
      return result;
    } catch (e) {
      _eventController.add(MCPConnectionEvent.toolExecutionFailed(server, toolName, e.toString()));
      rethrow;
    }
  }

  /// Read a resource from a specific server
  Future<Map<String, dynamic>> readResource({
    required String serverId,
    required String uri,
  }) async {
    final client = _connections[serverId];
    if (client == null) {
      throw mcp.MCPException('Not connected to server: $serverId');
    }

    return await client.readResource(uri);
  }

  /// Get a prompt from a specific server
  Future<Map<String, dynamic>> getPrompt({
    required String serverId,
    required String promptName,
    Map<String, dynamic>? arguments,
  }) async {
    final client = _connections[serverId];
    if (client == null) {
      throw mcp.MCPException('Not connected to server: $serverId');
    }

    return await client.getPrompt(promptName, arguments: arguments);
  }

  /// Connect to all auto-connect servers
  Future<void> connectToAutoConnectServers() async {
    final autoConnectServers = _serverConfigs.values
        .where((server) => server.autoConnect)
        .toList();

    await Future.wait(
      autoConnectServers.map((server) => connectToServer(server.id)),
    );
  }

  /// Disconnect from all servers
  Future<void> disconnectAll() async {
    final serverIds = _connections.keys.toList();
    await Future.wait(
      serverIds.map((serverId) => disconnectFromServer(serverId)),
    );
  }

  /// Dispose resources
  void dispose() {
    disconnectAll();
    _eventController.close();
  }
}

/// MCP Connection Events
class MCPConnectionEvent {
  final MCPConnectionEventType type;
  final MCPServer server;
  final String? message;
  final Map<String, dynamic>? data;
  final mcp.MCPNotification? notification;

  MCPConnectionEvent._({
    required this.type,
    required this.server,
    this.message,
    this.data,
    this.notification,
  });

  factory MCPConnectionEvent.serverAdded(MCPServer server) =>
      MCPConnectionEvent._(type: MCPConnectionEventType.serverAdded, server: server);

  factory MCPConnectionEvent.serverRemoved(MCPServer server) =>
      MCPConnectionEvent._(type: MCPConnectionEventType.serverRemoved, server: server);

  factory MCPConnectionEvent.connecting(MCPServer server) =>
      MCPConnectionEvent._(type: MCPConnectionEventType.connecting, server: server);

  factory MCPConnectionEvent.connected(MCPServer server, Map<String, dynamic> initResult) =>
      MCPConnectionEvent._(
        type: MCPConnectionEventType.connected,
        server: server,
        data: initResult,
      );

  factory MCPConnectionEvent.connectionFailed(MCPServer server, String error) =>
      MCPConnectionEvent._(
        type: MCPConnectionEventType.connectionFailed,
        server: server,
        message: error,
      );

  factory MCPConnectionEvent.disconnected(MCPServer server) =>
      MCPConnectionEvent._(type: MCPConnectionEventType.disconnected, server: server);

  factory MCPConnectionEvent.notificationReceived(MCPServer server, mcp.MCPNotification notification) =>
      MCPConnectionEvent._(
        type: MCPConnectionEventType.notificationReceived,
        server: server,
        notification: notification,
      );

  factory MCPConnectionEvent.toolExecuting(MCPServer server, String toolName) =>
      MCPConnectionEvent._(
        type: MCPConnectionEventType.toolExecuting,
        server: server,
        message: toolName,
      );

  factory MCPConnectionEvent.toolExecuted(MCPServer server, String toolName, Map<String, dynamic> result) =>
      MCPConnectionEvent._(
        type: MCPConnectionEventType.toolExecuted,
        server: server,
        message: toolName,
        data: result,
      );

  factory MCPConnectionEvent.toolExecutionFailed(MCPServer server, String toolName, String error) =>
      MCPConnectionEvent._(
        type: MCPConnectionEventType.toolExecutionFailed,
        server: server,
        message: '$toolName: $error',
      );
}

enum MCPConnectionEventType {
  serverAdded,
  serverRemoved,
  connecting,
  connected,
  connectionFailed,
  disconnected,
  notificationReceived,
  toolExecuting,
  toolExecuted,
  toolExecutionFailed,
}
import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';

/// MCP JSON-RPC Message Types
abstract class MCPMessage {
  final String jsonrpc = "2.0";
  Map<String, dynamic> toJson();
}

class MCPRequest extends MCPMessage {
  final dynamic id;
  final String method;
  final Map<String, dynamic>? params;

  MCPRequest({
    required this.id,
    required this.method,
    this.params,
  });

  @override
  Map<String, dynamic> toJson() => {
    'jsonrpc': jsonrpc,
    'id': id,
    'method': method,
    if (params != null) 'params': params,
  };
}

class MCPResponse extends MCPMessage {
  final dynamic id;
  final Map<String, dynamic>? result;
  final MCPError? error;

  MCPResponse({
    required this.id,
    this.result,
    this.error,
  });

  @override
  Map<String, dynamic> toJson() => {
    'jsonrpc': jsonrpc,
    'id': id,
    if (result != null) 'result': result,
    if (error != null) 'error': error!.toJson(),
  };

  factory MCPResponse.fromJson(Map<String, dynamic> json) {
    return MCPResponse(
      id: json['id'],
      result: json['result'],
      error: json['error'] != null ? MCPError.fromJson(json['error']) : null,
    );
  }
}

class MCPNotification extends MCPMessage {
  final String method;
  final Map<String, dynamic>? params;

  MCPNotification({
    required this.method,
    this.params,
  });

  @override
  Map<String, dynamic> toJson() => {
    'jsonrpc': jsonrpc,
    'method': method,
    if (params != null) 'params': params,
  };
}

class MCPError {
  final int code;
  final String message;
  final dynamic data;

  MCPError({
    required this.code,
    required this.message,
    this.data,
  });

  Map<String, dynamic> toJson() => {
    'code': code,
    'message': message,
    if (data != null) 'data': data,
  };

  factory MCPError.fromJson(Map<String, dynamic> json) {
    return MCPError(
      code: json['code'],
      message: json['message'],
      data: json['data'],
    );
  }
}

/// Transport implementations
abstract class MCPTransport {
  Stream<Map<String, dynamic>> get messages;
  Future<void> send(MCPMessage message);
  Future<void> close();
}

/// HTTP transport implementation
class MCPHttpTransport implements MCPTransport {
  final String baseUrl;
  final Dio _dio;
  final StreamController<Map<String, dynamic>> _messageController;
  String? _sessionId;

  MCPHttpTransport(this.baseUrl) 
    : _dio = Dio(),
      _messageController = StreamController<Map<String, dynamic>>.broadcast();

  @override
  Stream<Map<String, dynamic>> get messages => _messageController.stream;

  @override
  Future<void> send(MCPMessage message) async {
    try {
      final headers = <String, String>{
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/event-stream',
      };

      if (_sessionId != null) {
        headers['mcp-session-id'] = _sessionId!;
      }

      final response = await _dio.post(
        baseUrl,
        data: jsonEncode(message.toJson()),
        options: Options(headers: headers),
      );

      // Handle session ID
      final sessionId = response.headers.value('mcp-session-id');
      if (sessionId != null) {
        _sessionId = sessionId;
      }

      // Handle different response types
      if (response.headers.value('content-type')?.contains('text/event-stream') == true) {
        // SSE stream - handle server-to-client messages
        _handleSSEStream(response.data);
      } else if (response.statusCode == 200) {
        // Single JSON response
        final responseData = response.data;
        if (responseData is Map<String, dynamic>) {
          _messageController.add(responseData);
        }
      }
    } catch (e) {
      throw MCPException('Failed to send message: $e');
    }
  }

  void _handleSSEStream(String sseData) {
    final lines = sseData.split('\n');
    String? eventData;

    for (final line in lines) {
      if (line.startsWith('data: ')) {
        eventData = line.substring(6);
        if (eventData.isNotEmpty) {
          try {
            final json = jsonDecode(eventData);
            if (json is Map<String, dynamic>) {
              _messageController.add(json);
            }
          } catch (e) {
            // Ignore malformed JSON
          }
        }
      }
    }
  }

  @override
  Future<void> close() async {
    await _messageController.close();
  }
}

/// Process transport implementation for local MCP servers
class MCPProcessTransport implements MCPTransport {
  final String command;
  final List<String> args;
  late Process _process;
  final StreamController<Map<String, dynamic>> _messageController;
  late StreamSubscription _stdinSubscription;

  MCPProcessTransport({
    required this.command,
    required this.args,
  }) : _messageController = StreamController<Map<String, dynamic>>.broadcast();

  @override
  Stream<Map<String, dynamic>> get messages => _messageController.stream;

  Future<void> start() async {
    try {
      _process = await Process.start(command, args);
      
      // Listen to stdout for messages
      _stdinSubscription = _process.stdout
          .transform(utf8.decoder)
          .transform(const LineSplitter())
          .listen((line) {
        if (line.trim().isNotEmpty) {
          try {
            final json = jsonDecode(line);
            if (json is Map<String, dynamic>) {
              _messageController.add(json);
            }
          } catch (e) {
            // Ignore malformed JSON
          }
        }
      });
    } catch (e) {
      throw MCPException('Failed to start process: $e');
    }
  }

  @override
  Future<void> send(MCPMessage message) async {
    try {
      final jsonString = jsonEncode(message.toJson());
      _process.stdin.writeln(jsonString);
      await _process.stdin.flush();
    } catch (e) {
      throw MCPException('Failed to send message: $e');
    }
  }

  @override
  Future<void> close() async {
    await _stdinSubscription.cancel();
    _process.kill();
    await _messageController.close();
  }
}

/// Main MCP Client implementation
class MCPClient {
  final MCPTransport _transport;
  final Map<dynamic, Completer<MCPResponse>> _pendingRequests = {};
  final StreamController<MCPNotification> _notificationController;
  late StreamSubscription _messageSubscription;
  int _requestId = 1;

  MCPClient(this._transport)
    : _notificationController = StreamController<MCPNotification>.broadcast() {
    _messageSubscription = _transport.messages.listen(_handleMessage);
  }

  Stream<MCPNotification> get notifications => _notificationController.stream;

  void _handleMessage(Map<String, dynamic> json) {
    if (json.containsKey('id')) {
      // This is a response
      final response = MCPResponse.fromJson(json);
      final completer = _pendingRequests.remove(response.id);
      completer?.complete(response);
    } else if (json.containsKey('method')) {
      // This is a notification
      final notification = MCPNotification(
        method: json['method'],
        params: json['params'],
      );
      _notificationController.add(notification);
    }
  }

  /// Initialize connection with server
  Future<Map<String, dynamic>> initialize({
    required String clientName,
    required String clientVersion,
    Map<String, dynamic>? clientCapabilities,
  }) async {
    final response = await request(
      'initialize',
      params: {
        'protocolVersion': '2024-11-05',
        'capabilities': clientCapabilities ?? {},
        'clientInfo': {
          'name': clientName,
          'version': clientVersion,
        },
      },
    );

    if (response.error != null) {
      throw MCPException('Initialize failed: ${response.error!.message}');
    }

    // Send initialized notification
    await notification('notifications/initialized');

    return response.result ?? {};
  }

  /// Send a request and wait for response
  Future<MCPResponse> request(String method, {Map<String, dynamic>? params}) async {
    final id = _requestId++;
    final completer = Completer<MCPResponse>();
    _pendingRequests[id] = completer;

    final request = MCPRequest(
      id: id,
      method: method,
      params: params,
    );

    await _transport.send(request);
    return completer.future;
  }

  /// Send a one-way notification
  Future<void> notification(String method, {Map<String, dynamic>? params}) async {
    final notification = MCPNotification(
      method: method,
      params: params,
    );
    await _transport.send(notification);
  }

  /// List available tools
  Future<List<MCPTool>> listTools() async {
    final response = await request('tools/list');
    if (response.error != null) {
      throw MCPException('Failed to list tools: ${response.error!.message}');
    }

    final tools = response.result?['tools'] as List? ?? [];
    return tools
        .map((tool) => MCPTool.fromJson(tool))
        .toList();
  }

  /// Call a tool
  Future<Map<String, dynamic>> callTool(String name, {Map<String, dynamic>? arguments}) async {
    final response = await request('tools/call', params: {
      'name': name,
      if (arguments != null) 'arguments': arguments,
    });

    if (response.error != null) {
      throw MCPException('Tool call failed: ${response.error!.message}');
    }

    return response.result ?? {};
  }

  /// List available resources
  Future<List<MCPResource>> listResources() async {
    final response = await request('resources/list');
    if (response.error != null) {
      throw MCPException('Failed to list resources: ${response.error!.message}');
    }

    final resources = response.result?['resources'] as List? ?? [];
    return resources
        .map((resource) => MCPResource.fromJson(resource))
        .toList();
  }

  /// Read a resource
  Future<Map<String, dynamic>> readResource(String uri) async {
    final response = await request('resources/read', params: {
      'uri': uri,
    });

    if (response.error != null) {
      throw MCPException('Failed to read resource: ${response.error!.message}');
    }

    return response.result ?? {};
  }

  /// List available prompts
  Future<List<MCPPrompt>> listPrompts() async {
    final response = await request('prompts/list');
    if (response.error != null) {
      throw MCPException('Failed to list prompts: ${response.error!.message}');
    }

    final prompts = response.result?['prompts'] as List? ?? [];
    return prompts
        .map((prompt) => MCPPrompt.fromJson(prompt))
        .toList();
  }

  /// Get a prompt
  Future<Map<String, dynamic>> getPrompt(String name, {Map<String, dynamic>? arguments}) async {
    final response = await request('prompts/get', params: {
      'name': name,
      if (arguments != null) 'arguments': arguments,
    });

    if (response.error != null) {
      throw MCPException('Failed to get prompt: ${response.error!.message}');
    }

    return response.result ?? {};
  }

  /// Close the client connection
  Future<void> close() async {
    await _messageSubscription.cancel();
    await _notificationController.close();
    await _transport.close();
  }
}

/// MCP specific exception
class MCPException implements Exception {
  final String message;
  MCPException(this.message);

  @override
  String toString() => 'MCPException: $message';
}

/// MCP Tool representation
class MCPTool {
  final String name;
  final String? description;
  final Map<String, dynamic>? inputSchema;

  MCPTool({
    required this.name,
    this.description,
    this.inputSchema,
  });

  factory MCPTool.fromJson(Map<String, dynamic> json) {
    return MCPTool(
      name: json['name'],
      description: json['description'],
      inputSchema: json['inputSchema'],
    );
  }

  Map<String, dynamic> toJson() => {
    'name': name,
    if (description != null) 'description': description,
    if (inputSchema != null) 'inputSchema': inputSchema,
  };
}

/// MCP Resource representation
class MCPResource {
  final String uri;
  final String? name;
  final String? description;
  final String? mimeType;

  MCPResource({
    required this.uri,
    this.name,
    this.description,
    this.mimeType,
  });

  factory MCPResource.fromJson(Map<String, dynamic> json) {
    return MCPResource(
      uri: json['uri'],
      name: json['name'],
      description: json['description'],
      mimeType: json['mimeType'],
    );
  }

  Map<String, dynamic> toJson() => {
    'uri': uri,
    if (name != null) 'name': name,
    if (description != null) 'description': description,
    if (mimeType != null) 'mimeType': mimeType,
  };
}

/// MCP Prompt representation
class MCPPrompt {
  final String name;
  final String? description;
  final List<MCPPromptArgument>? arguments;

  MCPPrompt({
    required this.name,
    this.description,
    this.arguments,
  });

  factory MCPPrompt.fromJson(Map<String, dynamic> json) {
    final args = json['arguments'] as List?;
    return MCPPrompt(
      name: json['name'],
      description: json['description'],
      arguments: args?.map((arg) => MCPPromptArgument.fromJson(arg)).toList(),
    );
  }

  Map<String, dynamic> toJson() => {
    'name': name,
    if (description != null) 'description': description,
    if (arguments != null) 'arguments': arguments!.map((arg) => arg.toJson()).toList(),
  };
}

/// MCP Prompt Argument representation
class MCPPromptArgument {
  final String name;
  final String? description;
  final bool required;

  MCPPromptArgument({
    required this.name,
    this.description,
    this.required = false,
  });

  factory MCPPromptArgument.fromJson(Map<String, dynamic> json) {
    return MCPPromptArgument(
      name: json['name'],
      description: json['description'],
      required: json['required'] ?? false,
    );
  }

  Map<String, dynamic> toJson() => {
    'name': name,
    if (description != null) 'description': description,
    'required': required,
  };
}
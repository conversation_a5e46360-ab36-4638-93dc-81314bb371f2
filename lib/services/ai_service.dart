import 'dart:async';
import 'dart:convert';
import 'package:dio/dio.dart';
import '../models/models.dart';

/// AI service for handling chat completions with multiple providers
class AIService {
  final Dio _dio;
  static const String _openRouterBaseUrl = 'https://openrouter.ai/api/v1';
  
  AIService() : _dio = Dio() {
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 60);
  }

  /// Send a chat completion request to the specified AI provider
  Future<String> sendMessage({
    required AIProvider provider,
    required List<ChatMessage> messages,
    String? selectedModel,
    double temperature = 0.7,
    int? maxTokens,
    bool stream = false,
  }) async {
    switch (provider.type.toLowerCase()) {
      case 'openrouter':
        return _sendOpenRouterMessage(
          provider: provider,
          messages: messages,
          selectedModel: selectedModel,
          temperature: temperature,
          maxTokens: maxTokens,
          stream: stream,
        );
      case 'openai':
        return _sendOpenAIMessage(
          provider: provider,
          messages: messages,
          selectedModel: selectedModel,
          temperature: temperature,
          maxTokens: maxTokens,
          stream: stream,
        );
      case 'anthropic':
        return _sendAnthropicMessage(
          provider: provider,
          messages: messages,
          selectedModel: selectedModel,
          temperature: temperature,
          maxTokens: maxTokens,
          stream: stream,
        );
      default:
        throw AIServiceException('Unsupported provider type: ${provider.type}');
    }
  }

  /// Send a streaming chat completion request
  Stream<String> sendMessageStream({
    required AIProvider provider,
    required List<ChatMessage> messages,
    String? selectedModel,
    double temperature = 0.7,
    int? maxTokens,
  }) {
    switch (provider.type.toLowerCase()) {
      case 'openrouter':
        return _sendOpenRouterMessageStream(
          provider: provider,
          messages: messages,
          selectedModel: selectedModel,
          temperature: temperature,
          maxTokens: maxTokens,
        );
      case 'openai':
        return _sendOpenAIMessageStream(
          provider: provider,
          messages: messages,
          selectedModel: selectedModel,
          temperature: temperature,
          maxTokens: maxTokens,
        );
      default:
        throw AIServiceException('Streaming not supported for provider: ${provider.type}');
    }
  }

  /// OpenRouter API implementation
  Future<String> _sendOpenRouterMessage({
    required AIProvider provider,
    required List<ChatMessage> messages,
    String? selectedModel,
    double temperature = 0.7,
    int? maxTokens,
    bool stream = false,
  }) async {
    final model = selectedModel ?? provider.defaultModel ?? 'openai/gpt-3.5-turbo';
    
    final requestBody = {
      'model': model,
      'messages': messages.map((msg) => {
        'role': msg.isUser ? 'user' : 'assistant',
        'content': msg.content,
      }).toList(),
      'temperature': temperature,
      if (maxTokens != null) 'max_tokens': maxTokens,
      'stream': stream,
    };

    try {
      final response = await _dio.post(
        '$_openRouterBaseUrl/chat/completions',
        data: jsonEncode(requestBody),
        options: Options(
          headers: {
            'Authorization': 'Bearer ${provider.apiKey.trim()}',
            'Content-Type': 'application/json',
            'HTTP-Referer': 'https://varta.ai',
            'X-Title': 'Vaarta AI Chat',
            ...?provider.additionalHeaders,
          },
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['choices'] != null && data['choices'].isNotEmpty) {
          return data['choices'][0]['message']['content'] ?? 'No response content';
        }
        throw AIServiceException('No choices in response');
      } else {
        throw AIServiceException('API request failed: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw AIServiceException('Invalid API Key - Please check your API key in provider settings');
      } else if (e.response?.statusCode == 429) {
        throw AIServiceException('Rate limit exceeded - Please try again later');
      } else if (e.response?.statusCode == 400) {
        final errorMsg = e.response?.data['error']?['message'] ?? 'Bad request';
        throw AIServiceException('Request error: $errorMsg');
      } else if (e.response?.statusCode == 403) {
        throw AIServiceException('Access forbidden - Check your API key permissions');
      }
      throw AIServiceException('Network error: ${e.message}');
    } catch (e) {
      throw AIServiceException('Unexpected error: $e');
    }
  }

  /// OpenRouter streaming implementation
  Stream<String> _sendOpenRouterMessageStream({
    required AIProvider provider,
    required List<ChatMessage> messages,
    String? selectedModel,
    double temperature = 0.7,
    int? maxTokens,
  }) async* {
    final model = selectedModel ?? provider.defaultModel ?? 'openai/gpt-3.5-turbo';
    
    final requestBody = {
      'model': model,
      'messages': messages.map((msg) => {
        'role': msg.isUser ? 'user' : 'assistant',
        'content': msg.content,
      }).toList(),
      'temperature': temperature,
      if (maxTokens != null) 'max_tokens': maxTokens,
      'stream': true,
    };

    try {
      final response = await _dio.post(
        '$_openRouterBaseUrl/chat/completions',
        data: jsonEncode(requestBody),
        options: Options(
          headers: {
            'Authorization': 'Bearer ${provider.apiKey.trim()}',
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
            'HTTP-Referer': 'https://varta.ai',
            'X-Title': 'Vaarta AI Chat',
            ...?provider.additionalHeaders,
          },
          responseType: ResponseType.stream,
        ),
      );

      if (response.statusCode == 200) {
        await for (final chunk in _parseSSEStream(response.data.stream)) {
          if (chunk.isNotEmpty && chunk != '[DONE]') {
            try {
              final data = jsonDecode(chunk);
              final content = data['choices']?[0]?['delta']?['content'];
              if (content != null && content.isNotEmpty) {
                yield content;
              }
            } catch (e) {
              // Ignore malformed chunks
            }
          }
        }
      } else {
        throw AIServiceException('Streaming request failed: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw AIServiceException('Invalid API key');
      } else if (e.response?.statusCode == 429) {
        throw AIServiceException('Rate limit exceeded');
      }
      throw AIServiceException('Streaming error: ${e.message}');
    } catch (e) {
      throw AIServiceException('Streaming error: $e');
    }
  }

  /// Parse server-sent events stream
  Stream<String> _parseSSEStream(Stream<List<int>> stream) async* {
    final buffer = <int>[];
    
    await for (final chunk in stream) {
      buffer.addAll(chunk);
      
      // Process complete lines
      String data = utf8.decode(buffer, allowMalformed: true);
      final lines = data.split('\n');
      
      // Keep the last incomplete line in buffer
      if (lines.isNotEmpty && !data.endsWith('\n')) {
        final lastLine = lines.removeLast();
        buffer.clear();
        buffer.addAll(utf8.encode(lastLine));
      } else {
        buffer.clear();
      }
      
      // Process complete lines
      for (final line in lines) {
        if (line.startsWith('data: ')) {
          final eventData = line.substring(6).trim();
          if (eventData.isNotEmpty) {
            yield eventData;
          }
        }
      }
    }
  }

  /// OpenAI API implementation (direct)
  Future<String> _sendOpenAIMessage({
    required AIProvider provider,
    required List<ChatMessage> messages,
    String? selectedModel,
    double temperature = 0.7,
    int? maxTokens,
    bool stream = false,
  }) async {
    final baseUrl = provider.baseUrl ?? 'https://api.openai.com/v1';
    final model = selectedModel ?? provider.defaultModel ?? 'gpt-3.5-turbo';
    
    final requestBody = {
      'model': model,
      'messages': messages.map((msg) => {
        'role': msg.isUser ? 'user' : 'assistant',
        'content': msg.content,
      }).toList(),
      'temperature': temperature,
      if (maxTokens != null) 'max_tokens': maxTokens,
      'stream': stream,
    };

    try {
      final response = await _dio.post(
        '$baseUrl/chat/completions',
        data: jsonEncode(requestBody),
        options: Options(
          headers: {
            'Authorization': 'Bearer ${provider.apiKey.trim()}',
            'Content-Type': 'application/json',
            ...?provider.additionalHeaders,
          },
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['choices'] != null && data['choices'].isNotEmpty) {
          return data['choices'][0]['message']['content'] ?? 'No response content';
        }
        throw AIServiceException('No choices in response');
      } else {
        throw AIServiceException('API request failed: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw AIServiceException('Invalid API key');
      } else if (e.response?.statusCode == 429) {
        throw AIServiceException('Rate limit exceeded');
      }
      throw AIServiceException('Network error: ${e.message}');
    } catch (e) {
      throw AIServiceException('Unexpected error: $e');
    }
  }

  /// OpenAI streaming implementation
  Stream<String> _sendOpenAIMessageStream({
    required AIProvider provider,
    required List<ChatMessage> messages,
    String? selectedModel,
    double temperature = 0.7,
    int? maxTokens,
  }) async* {
    final baseUrl = provider.baseUrl ?? 'https://api.openai.com/v1';
    final model = selectedModel ?? provider.defaultModel ?? 'gpt-3.5-turbo';
    
    final requestBody = {
      'model': model,
      'messages': messages.map((msg) => {
        'role': msg.isUser ? 'user' : 'assistant',
        'content': msg.content,
      }).toList(),
      'temperature': temperature,
      if (maxTokens != null) 'max_tokens': maxTokens,
      'stream': true,
    };

    try {
      final response = await _dio.post(
        '$baseUrl/chat/completions',
        data: jsonEncode(requestBody),
        options: Options(
          headers: {
            'Authorization': 'Bearer ${provider.apiKey.trim()}',
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
            ...?provider.additionalHeaders,
          },
          responseType: ResponseType.stream,
        ),
      );

      if (response.statusCode == 200) {
        await for (final chunk in _parseSSEStream(response.data.stream)) {
          if (chunk.isNotEmpty && chunk != '[DONE]') {
            try {
              final data = jsonDecode(chunk);
              final content = data['choices']?[0]?['delta']?['content'];
              if (content != null && content.isNotEmpty) {
                yield content;
              }
            } catch (e) {
              // Ignore malformed chunks
            }
          }
        }
      } else {
        throw AIServiceException('Streaming request failed: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw AIServiceException('Invalid API key');
      }
      throw AIServiceException('Streaming error: ${e.message}');
    }
  }

  /// Anthropic API implementation (placeholder)
  Future<String> _sendAnthropicMessage({
    required AIProvider provider,
    required List<ChatMessage> messages,
    String? selectedModel,
    double temperature = 0.7,
    int? maxTokens,
    bool stream = false,
  }) async {
    // TODO: Implement Anthropic API integration
    throw AIServiceException('Anthropic API integration not yet implemented');
  }

  /// Get available models for a provider
  Future<List<String>> getAvailableModels(AIProvider provider) async {
    switch (provider.type.toLowerCase()) {
      case 'openrouter':
        return _getOpenRouterModels(provider);
      case 'openai':
        return _getOpenAIModels(provider);
      default:
        return provider.models;
    }
  }

  /// Get OpenRouter available models
  Future<List<String>> _getOpenRouterModels(AIProvider provider) async {
    try {
      final response = await _dio.get(
        '$_openRouterBaseUrl/models',
        options: Options(
          headers: {
            'Authorization': 'Bearer ${provider.apiKey.trim()}',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['data'] is List) {
          return (data['data'] as List)
              .map((model) => model['id'] as String)
              .toList();
        }
      }
      return provider.models;
    } catch (e) {
      return provider.models;
    }
  }

  /// Get OpenAI available models
  Future<List<String>> _getOpenAIModels(AIProvider provider) async {
    final baseUrl = provider.baseUrl ?? 'https://api.openai.com/v1';
    
    try {
      final response = await _dio.get(
        '$baseUrl/models',
        options: Options(
          headers: {
            'Authorization': 'Bearer ${provider.apiKey.trim()}',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['data'] is List) {
          return (data['data'] as List)
              .where((model) => model['id'].toString().contains('gpt'))
              .map((model) => model['id'] as String)
              .toList();
        }
      }
      return provider.models;
    } catch (e) {
      return provider.models;
    }
  }

  void dispose() {
    _dio.close();
  }
}

/// Exception for AI service errors
class AIServiceException implements Exception {
  final String message;
  AIServiceException(this.message);

  @override
  String toString() => 'AIServiceException: $message';
}

/// Chat message model for internal use
class ChatMessage {
  final String content;
  final bool isUser;
  final DateTime timestamp;

  ChatMessage({
    required this.content,
    required this.isUser,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();
}
import 'package:hive/hive.dart';

part 'mcp_server.g.dart';

@HiveType(typeId: 14)
enum MCPTransportType {
  @HiveField(0)
  http,
  @HiveField(1)
  stdio,
}

@HiveType(typeId: 15)
class MCPTransport extends HiveObject {
  @HiveField(0)
  final MCPTransportType type;

  @HiveField(1)
  final String? url;

  @HiveField(2)
  final String? command;

  @HiveField(3)
  final List<String>? args;

  @HiveField(4)
  final Map<String, String>? env;

  MCPTransport({
    required this.type,
    this.url,
    this.command,
    this.args,
    this.env,
  });

  MCPTransport copyWith({
    MCPTransportType? type,
    String? url,
    String? command,
    List<String>? args,
    Map<String, String>? env,
  }) {
    return MCPTransport(
      type: type ?? this.type,
      url: url ?? this.url,
      command: command ?? this.command,
      args: args ?? this.args,
      env: env ?? this.env,
    );
  }

  @override
  String toString() {
    return 'MCPTransport(type: $type, url: $url, command: $command)';
  }
}

@HiveType(typeId: 16)
class MCPServer extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String? description;

  @HiveField(3)
  final MCPTransport transport;

  @HiveField(4)
  final bool isActive;

  @HiveField(5)
  final DateTime createdAt;

  @HiveField(6)
  final DateTime updatedAt;

  @HiveField(7)
  final bool autoConnect;

  MCPServer({
    required this.id,
    required this.name,
    this.description,
    required this.transport,
    this.isActive = true,
    this.autoConnect = false,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  MCPServer copyWith({
    String? id,
    String? name,
    String? description,
    MCPTransport? transport,
    bool? isActive,
    bool? autoConnect,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return MCPServer(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      transport: transport ?? this.transport,
      isActive: isActive ?? this.isActive,
      autoConnect: autoConnect ?? this.autoConnect,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'MCPServer(id: $id, name: $name, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MCPServer && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
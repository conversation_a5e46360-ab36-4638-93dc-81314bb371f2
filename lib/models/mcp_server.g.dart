// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mcp_server.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MCPTransportAdapter extends TypeAdapter<MCPTransport> {
  @override
  final int typeId = 15;

  @override
  MCPTransport read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MCPTransport(
      type: fields[0] as MCPTransportType,
      url: fields[1] as String?,
      command: fields[2] as String?,
      args: (fields[3] as List?)?.cast<String>(),
      env: (fields[4] as Map?)?.cast<String, String>(),
    );
  }

  @override
  void write(BinaryWriter writer, MCPTransport obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.type)
      ..writeByte(1)
      ..write(obj.url)
      ..writeByte(2)
      ..write(obj.command)
      ..writeByte(3)
      ..write(obj.args)
      ..writeByte(4)
      ..write(obj.env);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MCPTransportAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class MCPServerAdapter extends TypeAdapter<MCPServer> {
  @override
  final int typeId = 16;

  @override
  MCPServer read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MCPServer(
      id: fields[0] as String,
      name: fields[1] as String,
      description: fields[2] as String?,
      transport: fields[3] as MCPTransport,
      isActive: fields[4] as bool,
      autoConnect: fields[7] as bool,
      createdAt: fields[5] as DateTime?,
      updatedAt: fields[6] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, MCPServer obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.transport)
      ..writeByte(4)
      ..write(obj.isActive)
      ..writeByte(5)
      ..write(obj.createdAt)
      ..writeByte(6)
      ..write(obj.updatedAt)
      ..writeByte(7)
      ..write(obj.autoConnect);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MCPServerAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class MCPTransportTypeAdapter extends TypeAdapter<MCPTransportType> {
  @override
  final int typeId = 14;

  @override
  MCPTransportType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return MCPTransportType.http;
      case 1:
        return MCPTransportType.stdio;
      default:
        return MCPTransportType.http;
    }
  }

  @override
  void write(BinaryWriter writer, MCPTransportType obj) {
    switch (obj) {
      case MCPTransportType.http:
        writer.writeByte(0);
        break;
      case MCPTransportType.stdio:
        writer.writeByte(1);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MCPTransportTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

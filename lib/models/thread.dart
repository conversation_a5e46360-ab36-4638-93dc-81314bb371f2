import 'package:hive/hive.dart';
import 'message.dart';

part 'thread.g.dart';

@HiveType(typeId: 1)
class Thread extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String spaceId;

  @HiveField(2)
  final String title;

  @HiveField(3)
  final DateTime created;

  @HiveField(4)
  final List<Message> messages;

  @HiveField(5)
  final String modelId;

  @HiveField(6)
  final String? personaId;

  Thread({
    required this.id,
    required this.spaceId,
    required this.title,
    required this.created,
    required this.messages,
    required this.modelId,
    this.personaId,
  });

  Thread copyWith({
    String? title,
    List<Message>? messages,
    String? modelId,
    String? personaId,
  }) {
    return Thread(
      id: id,
      spaceId: spaceId,
      title: title ?? this.title,
      created: created,
      messages: messages ?? this.messages,
      modelId: modelId ?? this.modelId,
      personaId: personaId ?? this.personaId,
    );
  }

  @override
  String toString() {
    return 'Thread(id: $id, spaceId: $spaceId, title: $title, modelId: $modelId, messages: ${messages.length})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Thread && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ai_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AIModelAdapter extends TypeAdapter<AIModel> {
  @override
  final int typeId = 8;

  @override
  AIModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AIModel(
      id: fields[0] as String,
      name: fields[1] as String,
      provider: fields[2] as String,
      supportsStreaming: fields[3] as bool,
      supportsTools: fields[4] as bool,
      contextLength: fields[5] as int?,
      costPerInputToken: fields[6] as double?,
      costPerOutputToken: fields[7] as double?,
    );
  }

  @override
  void write(BinaryWriter writer, AIModel obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.provider)
      ..writeByte(3)
      ..write(obj.supportsStreaming)
      ..writeByte(4)
      ..write(obj.supportsTools)
      ..writeByte(5)
      ..write(obj.contextLength)
      ..writeByte(6)
      ..write(obj.costPerInputToken)
      ..writeByte(7)
      ..write(obj.costPerOutputToken);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AIModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

import 'package:hive/hive.dart';

part 'mcp_tool.g.dart';

@HiveType(typeId: 11)
class McpTool extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String description;

  @HiveField(3)
  final Map<String, dynamic> parameters;

  @HiveField(4)
  final String serverId;

  McpTool({
    required this.id,
    required this.name,
    required this.description,
    required this.parameters,
    required this.serverId,
  });

  McpTool copyWith({
    String? name,
    String? description,
    Map<String, dynamic>? parameters,
    String? serverId,
  }) {
    return McpTool(
      id: id,
      name: name ?? this.name,
      description: description ?? this.description,
      parameters: parameters ?? this.parameters,
      serverId: serverId ?? this.serverId,
    );
  }

  @override
  String toString() {
    return 'McpTool(id: $id, name: $name, serverId: $serverId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is McpTool && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tool_call.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ToolCallAdapter extends TypeAdapter<ToolCall> {
  @override
  final int typeId = 12;

  @override
  ToolCall read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ToolCall(
      id: fields[0] as String,
      toolId: fields[1] as String,
      toolName: fields[2] as String,
      arguments: (fields[3] as Map).cast<String, dynamic>(),
      timestamp: fields[4] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, ToolCall obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.toolId)
      ..writeByte(2)
      ..write(obj.toolName)
      ..writeByte(3)
      ..write(obj.arguments)
      ..writeByte(4)
      ..write(obj.timestamp);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ToolCallAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

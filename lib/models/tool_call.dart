import 'package:hive/hive.dart';
import 'package:equatable/equatable.dart';

part 'tool_call.g.dart';

@HiveType(typeId: 12)
class ToolCall extends HiveObject with EquatableMixin {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String toolId;

  @HiveField(2)
  final String toolName;

  @HiveField(3)
  final Map<String, dynamic> arguments;

  @HiveField(4)
  final DateTime? timestamp;

  ToolCall({
    required this.id,
    required this.toolId,
    required this.toolName,
    required this.arguments,
    this.timestamp,
  });

  factory ToolCall.fromJson(Map<String, dynamic> json) {
    return ToolCall(
      id: json['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
      toolId: json['tool_id'] ?? json['function']?['name'] ?? '',
      toolName: json['tool_name'] ?? json['function']?['name'] ?? '',
      arguments: json['arguments'] ?? json['function']?['arguments'] ?? {},
      timestamp: json['timestamp'] != null 
          ? DateTime.parse(json['timestamp'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'tool_id': toolId,
      'tool_name': toolName,
      'arguments': arguments,
      if (timestamp != null) 'timestamp': timestamp!.toIso8601String(),
    };
  }

  ToolCall copyWith({
    String? id,
    String? toolId,
    String? toolName,
    Map<String, dynamic>? arguments,
    DateTime? timestamp,
  }) {
    return ToolCall(
      id: id ?? this.id,
      toolId: toolId ?? this.toolId,
      toolName: toolName ?? this.toolName,
      arguments: arguments ?? this.arguments,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  @override
  List<Object?> get props => [id, toolId, toolName, arguments, timestamp];

  @override
  String toString() {
    return 'ToolCall(id: $id, toolName: $toolName, arguments: $arguments)';
  }
}

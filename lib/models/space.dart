import 'package:hive/hive.dart';

part 'space.g.dart';

@HiveType(typeId: 0)
class Space extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String title;

  @HiveField(2)
  final String? description;

  @HiveField(3)
  final String? icon;

  @HiveField(4)
  final String? systemPrompt;

  @HiveField(5)
  final DateTime created;

  @HiveField(6)
  final List<String> threadIds;

  @HiveField(7)
  final String? defaultPersonaId;

  @HiveField(8)
  final DateTime updatedAt;

  Space({
    required this.id,
    required this.title,
    this.description,
    this.icon,
    this.systemPrompt,
    required this.created,
    required this.threadIds,
    this.defaultPersonaId,
    DateTime? updatedAt,
  }) : updatedAt = updatedAt ?? DateTime.now();

  Space copyWith({
    String? title,
    String? description,
    String? icon,
    String? systemPrompt,
    List<String>? threadIds,
    String? defaultPersonaId,
    DateTime? updatedAt,
  }) {
    return Space(
      id: id,
      title: title ?? this.title,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      systemPrompt: systemPrompt ?? this.systemPrompt,
      created: created,
      threadIds: threadIds ?? this.threadIds,
      defaultPersonaId: defaultPersonaId ?? this.defaultPersonaId,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'Space(id: $id, title: $title, description: $description, icon: $icon, created: $created)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Space && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
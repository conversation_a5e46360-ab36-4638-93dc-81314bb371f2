import 'package:hive/hive.dart';

part 'ai_model.g.dart';

@HiveType(typeId: 8)
class AIModel extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String provider;

  @HiveField(3)
  final bool supportsStreaming;

  @HiveField(4)
  final bool supportsTools;

  @HiveField(5)
  final int? contextLength;

  @HiveField(6)
  final double? costPerInputToken;

  @HiveField(7)
  final double? costPerOutputToken;

  AIModel({
    required this.id,
    required this.name,
    required this.provider,
    required this.supportsStreaming,
    required this.supportsTools,
    this.contextLength,
    this.costPerInputToken,
    this.costPerOutputToken,
  });

  AIModel copyWith({
    String? name,
    String? provider,
    bool? supportsStreaming,
    bool? supportsTools,
    int? contextLength,
    double? costPerInputToken,
    double? costPerOutputToken,
  }) {
    return AIModel(
      id: id,
      name: name ?? this.name,
      provider: provider ?? this.provider,
      supportsStreaming: supportsStreaming ?? this.supportsStreaming,
      supportsTools: supportsTools ?? this.supportsTools,
      contextLength: contextLength ?? this.contextLength,
      costPerInputToken: costPerInputToken ?? this.costPerInputToken,
      costPerOutputToken: costPerOutputToken ?? this.costPerOutputToken,
    );
  }

  @override
  String toString() {
    return 'AIModel(id: $id, name: $name, provider: $provider)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AIModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
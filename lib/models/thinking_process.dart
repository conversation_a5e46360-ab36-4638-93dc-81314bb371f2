import 'package:hive/hive.dart';

part 'thinking_process.g.dart';

@HiveType(typeId: 3)
class ThinkingProcess extends HiveObject {
  @HiveField(0)
  final List<ThinkingStep> steps;

  @HiveField(1)
  final DateTime timestamp;

  ThinkingProcess({
    required this.steps,
    required this.timestamp,
  });

  ThinkingProcess copyWith({
    List<ThinkingStep>? steps,
  }) {
    return ThinkingProcess(
      steps: steps ?? this.steps,
      timestamp: timestamp,
    );
  }

  @override
  String toString() {
    return 'ThinkingProcess(steps: ${steps.length}, timestamp: $timestamp)';
  }
}

@HiveType(typeId: 4)
class ThinkingStep extends HiveObject {
  @HiveField(0)
  final int stepNumber;

  @HiveField(1)
  final String description;

  @HiveField(2)
  final String content;

  @HiveField(3)
  final bool isCompleted;

  ThinkingStep({
    required this.stepNumber,
    required this.description,
    required this.content,
    required this.isCompleted,
  });

  ThinkingStep copyWith({
    String? description,
    String? content,
    bool? isCompleted,
  }) {
    return ThinkingStep(
      stepNumber: stepNumber,
      description: description ?? this.description,
      content: content ?? this.content,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }

  @override
  String toString() {
    return 'ThinkingStep(step: $stepNumber, description: $description, completed: $isCompleted)';
  }
}
import 'package:hive/hive.dart';

part 'ai_provider.g.dart';

@HiveType(typeId: 10)
class AIProvider extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String apiKey;

  @HiveField(3)
  final String? baseUrl;

  @HiveField(4)
  final String? model;

  @HiveField(5)
  final Map<String, dynamic>? additionalHeaders;

  @HiveField(6)
  final bool isActive;

  @HiveField(7)
  final DateTime createdAt;

  @HiveField(8)
  final DateTime updatedAt;

  @HiveField(9)
  final String type;

  @HiveField(10)
  final String? defaultModel;

  @HiveField(11)
  final List<String> models;

  AIProvider({
    required this.id,
    required this.name,
    required this.apiKey,
    required this.type,
    this.baseUrl,
    this.model,
    this.defaultModel,
    this.models = const [],
    this.additionalHeaders,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  AIProvider copyWith({
    String? id,
    String? name,
    String? apiKey,
    String? type,
    String? baseUrl,
    String? model,
    String? defaultModel,
    List<String>? models,
    Map<String, dynamic>? additionalHeaders,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AIProvider(
      id: id ?? this.id,
      name: name ?? this.name,
      apiKey: apiKey ?? this.apiKey,
      type: type ?? this.type,
      baseUrl: baseUrl ?? this.baseUrl,
      model: model ?? this.model,
      defaultModel: defaultModel ?? this.defaultModel,
      models: models ?? this.models,
      additionalHeaders: additionalHeaders ?? this.additionalHeaders,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'AIProvider(id: $id, name: $name, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AIProvider && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
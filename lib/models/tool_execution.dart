import 'package:hive/hive.dart';

part 'tool_execution.g.dart';

@HiveType(typeId: 5)
enum ExecutionStatus {
  @HiveField(0)
  pending,
  
  @HiveField(1)
  running,
  
  @HiveField(2)
  completed,
  
  @HiveField(3)
  failed,
}

@HiveType(typeId: 6)
class ToolExecution extends HiveObject {
  @HiveField(0)
  final String toolId;

  @HiveField(1)
  final Map<String, dynamic> input;

  @HiveField(2)
  final String? result;

  @HiveField(3)
  final DateTime timestamp;

  @HiveField(4)
  final ExecutionStatus status;

  @HiveField(5)
  final String? errorMessage;

  ToolExecution({
    required this.toolId,
    required this.input,
    this.result,
    required this.timestamp,
    required this.status,
    this.errorMessage,
  });

  ToolExecution copyWith({
    String? result,
    ExecutionStatus? status,
    String? errorMessage,
  }) {
    return ToolExecution(
      toolId: toolId,
      input: input,
      result: result ?? this.result,
      timestamp: timestamp,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  String toString() {
    return 'ToolExecution(toolId: $toolId, status: $status, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ToolExecution &&
        other.toolId == toolId &&
        other.timestamp == timestamp;
  }

  @override
  int get hashCode => Object.hash(toolId, timestamp);
}
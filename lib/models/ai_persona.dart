import 'package:hive/hive.dart';

part 'ai_persona.g.dart';

@HiveType(typeId: 7)
class AIPersona extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String description;

  @HiveField(3)
  final String systemPrompt;

  @HiveField(4)
  final String avatar; // emoji

  @HiveField(5)
  final List<String> specialties;

  @HiveField(6)
  final bool isBuiltIn;

  AIPersona({
    required this.id,
    required this.name,
    required this.description,
    required this.systemPrompt,
    required this.avatar,
    required this.specialties,
    required this.isBuiltIn,
  });

  AIPersona copyWith({
    String? name,
    String? description,
    String? systemPrompt,
    String? avatar,
    List<String>? specialties,
    bool? isBuiltIn,
  }) {
    return AIPersona(
      id: id,
      name: name ?? this.name,
      description: description ?? this.description,
      systemPrompt: systemPrompt ?? this.systemPrompt,
      avatar: avatar ?? this.avatar,
      specialties: specialties ?? this.specialties,
      isBuiltIn: isBuiltIn ?? this.isBuiltIn,
    );
  }

  @override
  String toString() {
    return 'AIPersona(id: $id, name: $name, avatar: $avatar, builtin: $isBuiltIn)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AIPersona && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
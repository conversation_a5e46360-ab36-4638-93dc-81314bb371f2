// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'thinking_process.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ThinkingProcessAdapter extends TypeAdapter<ThinkingProcess> {
  @override
  final int typeId = 3;

  @override
  ThinkingProcess read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ThinkingProcess(
      steps: (fields[0] as List).cast<ThinkingStep>(),
      timestamp: fields[1] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, ThinkingProcess obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.steps)
      ..writeByte(1)
      ..write(obj.timestamp);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ThinkingProcessAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ThinkingStepAdapter extends TypeAdapter<ThinkingStep> {
  @override
  final int typeId = 4;

  @override
  ThinkingStep read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ThinkingStep(
      stepNumber: fields[0] as int,
      description: fields[1] as String,
      content: fields[2] as String,
      isCompleted: fields[3] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, ThinkingStep obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.stepNumber)
      ..writeByte(1)
      ..write(obj.description)
      ..writeByte(2)
      ..write(obj.content)
      ..writeByte(3)
      ..write(obj.isCompleted);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ThinkingStepAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

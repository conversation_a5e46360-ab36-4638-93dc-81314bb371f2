// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tool_execution.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ToolExecutionAdapter extends TypeAdapter<ToolExecution> {
  @override
  final int typeId = 6;

  @override
  ToolExecution read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ToolExecution(
      toolId: fields[0] as String,
      input: (fields[1] as Map).cast<String, dynamic>(),
      result: fields[2] as String?,
      timestamp: fields[3] as DateTime,
      status: fields[4] as ExecutionStatus,
      errorMessage: fields[5] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, ToolExecution obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.toolId)
      ..writeByte(1)
      ..write(obj.input)
      ..writeByte(2)
      ..write(obj.result)
      ..writeByte(3)
      ..write(obj.timestamp)
      ..writeByte(4)
      ..write(obj.status)
      ..writeByte(5)
      ..write(obj.errorMessage);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ToolExecutionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ExecutionStatusAdapter extends TypeAdapter<ExecutionStatus> {
  @override
  final int typeId = 5;

  @override
  ExecutionStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ExecutionStatus.pending;
      case 1:
        return ExecutionStatus.running;
      case 2:
        return ExecutionStatus.completed;
      case 3:
        return ExecutionStatus.failed;
      default:
        return ExecutionStatus.pending;
    }
  }

  @override
  void write(BinaryWriter writer, ExecutionStatus obj) {
    switch (obj) {
      case ExecutionStatus.pending:
        writer.writeByte(0);
        break;
      case ExecutionStatus.running:
        writer.writeByte(1);
        break;
      case ExecutionStatus.completed:
        writer.writeByte(2);
        break;
      case ExecutionStatus.failed:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ExecutionStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ai_provider.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AIProviderAdapter extends TypeAdapter<AIProvider> {
  @override
  final int typeId = 10;

  @override
  AIProvider read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AIProvider(
      id: fields[0] as String,
      name: fields[1] as String,
      apiKey: fields[2] as String,
      type: fields[9] as String,
      baseUrl: fields[3] as String?,
      model: fields[4] as String?,
      defaultModel: fields[10] as String?,
      models: (fields[11] as List).cast<String>(),
      additionalHeaders: (fields[5] as Map?)?.cast<String, dynamic>(),
      isActive: fields[6] as bool,
      createdAt: fields[7] as DateTime?,
      updatedAt: fields[8] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, AIProvider obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.apiKey)
      ..writeByte(3)
      ..write(obj.baseUrl)
      ..writeByte(4)
      ..write(obj.model)
      ..writeByte(5)
      ..write(obj.additionalHeaders)
      ..writeByte(6)
      ..write(obj.isActive)
      ..writeByte(7)
      ..write(obj.createdAt)
      ..writeByte(8)
      ..write(obj.updatedAt)
      ..writeByte(9)
      ..write(obj.type)
      ..writeByte(10)
      ..write(obj.defaultModel)
      ..writeByte(11)
      ..write(obj.models);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AIProviderAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

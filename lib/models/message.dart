import 'package:hive/hive.dart';
import 'thinking_process.dart';
import 'tool_execution.dart';

part 'message.g.dart';

@HiveType(typeId: 2)
class Message extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String content;

  @HiveField(2)
  final bool isUser;

  @HiveField(3)
  final DateTime timestamp;

  @HiveField(4)
  final String? modelUsed;

  @HiveField(5)
  final String? personaUsed;

  @HiveField(6)
  final ThinkingProcess? reasoning;

  @HiveField(7)
  final List<ToolExecution>? toolResults;

  Message({
    required this.id,
    required this.content,
    required this.isUser,
    required this.timestamp,
    this.modelUsed,
    this.personaUsed,
    this.reasoning,
    this.toolResults,
  });

  Message copyWith({
    String? content,
    bool? isUser,
    String? modelUsed,
    String? personaUsed,
    ThinkingProcess? reasoning,
    List<ToolExecution>? toolResults,
  }) {
    return Message(
      id: id,
      content: content ?? this.content,
      isUser: isUser ?? this.isUser,
      timestamp: timestamp,
      modelUsed: modelUsed ?? this.modelUsed,
      personaUsed: personaUsed ?? this.personaUsed,
      reasoning: reasoning ?? this.reasoning,
      toolResults: toolResults ?? this.toolResults,
    );
  }

  @override
  String toString() {
    return 'Message(id: $id, isUser: $isUser, content: ${content.substring(0, content.length > 50 ? 50 : content.length)}...)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Message && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
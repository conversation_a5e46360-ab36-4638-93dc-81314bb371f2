// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mcp_tool.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class McpToolAdapter extends TypeAdapter<McpTool> {
  @override
  final int typeId = 11;

  @override
  McpTool read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return McpTool(
      id: fields[0] as String,
      name: fields[1] as String,
      description: fields[2] as String,
      parameters: (fields[3] as Map).cast<String, dynamic>(),
      serverId: fields[4] as String,
    );
  }

  @override
  void write(BinaryWriter writer, McpTool obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.parameters)
      ..writeByte(4)
      ..write(obj.serverId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is McpToolAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ai_persona.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AIPersonaAdapter extends TypeAdapter<AIPersona> {
  @override
  final int typeId = 7;

  @override
  AIPersona read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AIPersona(
      id: fields[0] as String,
      name: fields[1] as String,
      description: fields[2] as String,
      systemPrompt: fields[3] as String,
      avatar: fields[4] as String,
      specialties: (fields[5] as List).cast<String>(),
      isBuiltIn: fields[6] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, AIPersona obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.systemPrompt)
      ..writeByte(4)
      ..write(obj.avatar)
      ..writeByte(5)
      ..write(obj.specialties)
      ..writeByte(6)
      ..write(obj.isBuiltIn);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AIPersonaAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive/hive.dart';
import '../../data/repositories/repositories.dart';
import '../../models/models.dart';
import 'spaces_state.dart';

class SpacesCubit extends Cubit<SpacesState> {
  final SpaceRepository _spaceRepository;
  final ThreadRepository _threadRepository;
  StreamSubscription<BoxEvent>? _spacesSubscription;

  SpacesCubit({
    required SpaceRepository spaceRepository,
    required ThreadRepository threadRepository,
  })  : _spaceRepository = spaceRepository,
        _threadRepository = threadRepository,
        super(SpacesInitial()) {
    _watchSpaces();
    loadSpaces();
  }

  void _watchSpaces() {
    _spacesSubscription = _spaceRepository.watchSpaces().listen((_) {
      loadSpaces();
    });
  }

  Future<void> loadSpaces() async {
    try {
      emit(SpacesLoading());
      final spaces = await _spaceRepository.getAllSpaces();
      emit(SpacesLoaded(spaces));
    } catch (e) {
      emit(SpacesError(e.toString()));
    }
  }

  Future<void> createSpace({
    required String title,
    String? description,
    String? icon,
    String? systemPrompt,
    String? defaultPersonaId,
  }) async {
    try {
      emit(SpaceCreating());
      final space = await _spaceRepository.createSpace(
        title: title,
        description: description,
        icon: icon,
        systemPrompt: systemPrompt,
        defaultPersonaId: defaultPersonaId,
      );
      emit(SpaceCreated(space));
      // Reload spaces to show the updated state
      await loadSpaces();
    } catch (e) {
      emit(SpacesError(e.toString()));
    }
  }

  Future<void> updateSpace(Space space) async {
    try {
      await _spaceRepository.updateSpace(space);
      // loadSpaces will be called automatically by the watch stream
    } catch (e) {
      emit(SpacesError(e.toString()));
    }
  }

  Future<void> deleteSpace(String spaceId) async {
    try {
      // Delete all threads in this space first
      await _threadRepository.deleteThreadsForSpace(spaceId);
      // Then delete the space
      await _spaceRepository.deleteSpace(spaceId);
      // loadSpaces will be called automatically by the watch stream
    } catch (e) {
      emit(SpacesError(e.toString()));
    }
  }

  @override
  Future<void> close() {
    _spacesSubscription?.cancel();
    return super.close();
  }
}
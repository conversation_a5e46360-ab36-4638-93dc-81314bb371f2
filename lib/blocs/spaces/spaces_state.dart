import 'package:equatable/equatable.dart';
import '../../models/models.dart';

abstract class SpacesState extends Equatable {
  const SpacesState();

  @override
  List<Object?> get props => [];
}

class SpacesInitial extends SpacesState {}

class SpacesLoading extends SpacesState {}

class SpacesLoaded extends SpacesState {
  final List<Space> spaces;

  const SpacesLoaded(this.spaces);

  @override
  List<Object?> get props => [spaces];
}

class SpacesError extends SpacesState {
  final String message;

  const SpacesError(this.message);

  @override
  List<Object?> get props => [message];
}

class SpaceCreating extends SpacesState {}

class SpaceCreated extends SpacesState {
  final Space space;

  const SpaceCreated(this.space);

  @override
  List<Object?> get props => [space];
}
part of 'mcp_tools_cubit.dart';

abstract class MCPToolsState extends Equatable {
  const MCPToolsState();

  const factory MCPToolsState.initial() = MCPToolsInitial;
  const factory MCPToolsState.loading() = MCPToolsLoading;
  const factory MCPToolsState.loaded({
    required Map<String, List<McpTool>> toolsByServer,
    required Map<String, bool> serverConnectionStatus,
  }) = MCPToolsLoaded;
  const factory MCPToolsState.error(String message) = MCPToolsError;

  @override
  List<Object?> get props => [];
}

class MCPToolsInitial extends MCPToolsState {
  const MCPToolsInitial();
}

class MCPToolsLoading extends MCPToolsState {
  const MCPToolsLoading();
}

class MCPToolsLoaded extends MCPToolsState {
  final Map<String, List<McpTool>> toolsByServer;
  final Map<String, bool> serverConnectionStatus;

  const MCPToolsLoaded({
    required this.toolsByServer,
    required this.serverConnectionStatus,
  });

  /// Get all tools across all servers
  List<McpTool> get allTools {
    return toolsByServer.values.expand((tools) => tools).toList();
  }

  /// Get tools for a specific server
  List<McpTool> getToolsForServer(String serverId) {
    return toolsByServer[serverId] ?? [];
  }

  /// Check if a server is connected
  bool isServerConnected(String serverId) {
    return serverConnectionStatus[serverId] ?? false;
  }

  /// Get connected servers
  List<String> get connectedServers {
    return serverConnectionStatus.entries
        .where((entry) => entry.value)
        .map((entry) => entry.key)
        .toList();
  }

  /// Get total number of tools
  int get totalToolCount {
    return toolsByServer.values.fold(0, (sum, tools) => sum + tools.length);
  }

  MCPToolsLoaded copyWith({
    Map<String, List<McpTool>>? toolsByServer,
    Map<String, bool>? serverConnectionStatus,
  }) {
    return MCPToolsLoaded(
      toolsByServer: toolsByServer ?? this.toolsByServer,
      serverConnectionStatus: serverConnectionStatus ?? this.serverConnectionStatus,
    );
  }

  @override
  List<Object> get props => [toolsByServer, serverConnectionStatus];
}

class MCPToolsError extends MCPToolsState {
  final String message;

  const MCPToolsError(this.message);

  @override
  List<Object> get props => [message];
}

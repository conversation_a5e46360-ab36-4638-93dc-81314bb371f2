import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../models/models.dart';
import '../../services/mcp_connection_manager.dart';
import '../../services/mcp_client.dart' as mcp;
import '../../data/hive_storage.dart';

part 'mcp_tools_state.dart';

class MCPToolsCubit extends Cubit<MCPToolsState> {
  MCPToolsCubit() : super(const MCPToolsState.initial());

  final MCPConnectionManager _connectionManager = MCPConnectionManager();
  StreamSubscription<MCPConnectionEvent>? _connectionSubscription;

  /// Initialize and start listening to MCP connection events
  Future<void> initialize() async {
    emit(const MCPToolsState.loading());

    try {
      // Load configured servers from storage
      final servers = await HiveStorage.getMCPServers();

      // Initialize connection manager with servers
      for (final server in servers.values) {
        await _connectionManager.addServer(server);
      }

      // Listen to connection events
      _connectionSubscription = _connectionManager.events.listen(
        _handleConnectionEvent,
      );

      // Connect to auto-connect servers
      await _connectionManager.connectToAutoConnectServers();

      // Load initial tools
      await _loadTools();
    } catch (e) {
      emit(MCPToolsState.error('Failed to initialize MCP tools: $e'));
    }
  }

  /// Load tools from all connected servers
  Future<void> _loadTools() async {
    try {
      final toolsByServer = <String, List<McpTool>>{};
      final serverConnectionStatus = <String, bool>{};

      // Get all configured servers
      final servers = _connectionManager.servers;

      for (final server in servers) {
        final isConnected = _connectionManager.activeConnections.contains(
          server.id,
        );
        serverConnectionStatus[server.id] = isConnected;

        if (isConnected) {
          try {
            // Get tools from this server
            final client = _connectionManager.getClient(server.id);
            if (client != null) {
              final tools = await client.listTools();
              toolsByServer[server.id] = tools
                  .map(
                    (mcpTool) => McpTool(
                      id: '${server.id}:${mcpTool.name}',
                      name: mcpTool.name,
                      description: mcpTool.description ?? '',
                      parameters: mcpTool.inputSchema ?? {},
                      serverId: server.id,
                    ),
                  )
                  .toList();
            } else {
              toolsByServer[server.id] = [];
            }
          } catch (e) {
            // Server connection failed, mark as disconnected
            serverConnectionStatus[server.id] = false;
            toolsByServer[server.id] = [];
          }
        } else {
          toolsByServer[server.id] = [];
        }
      }

      emit(
        MCPToolsState.loaded(
          toolsByServer: toolsByServer,
          serverConnectionStatus: serverConnectionStatus,
        ),
      );
    } catch (e) {
      emit(MCPToolsState.error('Failed to load tools: $e'));
    }
  }

  /// Handle MCP connection events
  void _handleConnectionEvent(MCPConnectionEvent event) {
    switch (event.type) {
      case MCPConnectionEventType.connected:
      case MCPConnectionEventType.disconnected:
      case MCPConnectionEventType.connectionFailed:
        // Reload tools when connection status changes
        _loadTools();
        break;
      case MCPConnectionEventType.notificationReceived:
        // Handle tool list change notifications
        if (event.notification?.method == 'notifications/tools/list_changed') {
          _loadTools();
        }
        break;
      default:
        // Ignore other event types
        break;
    }
  }

  /// Refresh tools from all servers
  Future<void> refreshTools() async {
    emit(const MCPToolsState.loading());
    await _loadTools();
  }

  /// Get tool by ID
  McpTool? getToolById(String toolId) {
    final currentState = state;
    if (currentState is MCPToolsLoaded) {
      for (final tools in currentState.toolsByServer.values) {
        final tool = tools.where((t) => t.id == toolId).isNotEmpty
            ? tools.where((t) => t.id == toolId).first
            : null;
        if (tool != null) return tool;
      }
    }
    return null;
  }

  /// Get tools for a specific server
  List<McpTool> getToolsForServer(String serverId) {
    final currentState = state;
    if (currentState is MCPToolsLoaded) {
      return currentState.getToolsForServer(serverId);
    }
    return [];
  }

  /// Check if a server is connected
  bool isServerConnected(String serverId) {
    final currentState = state;
    if (currentState is MCPToolsLoaded) {
      return currentState.isServerConnected(serverId);
    }
    return false;
  }

  @override
  Future<void> close() {
    _connectionSubscription?.cancel();
    return super.close();
  }
}

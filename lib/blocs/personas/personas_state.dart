import 'package:equatable/equatable.dart';
import '../../models/models.dart';

abstract class PersonasState extends Equatable {
  const PersonasState();

  @override
  List<Object?> get props => [];
}

class PersonasInitial extends PersonasState {}

class PersonasLoading extends PersonasState {}

class PersonasLoaded extends PersonasState {
  final List<AIPersona> personas;
  final AIPersona? selectedPersona;

  const PersonasLoaded(this.personas, {this.selectedPersona});

  @override
  List<Object?> get props => [personas, selectedPersona];

  PersonasLoaded copyWith({
    List<AIPersona>? personas,
    AIPersona? selectedPersona,
  }) {
    return PersonasLoaded(
      personas ?? this.personas,
      selectedPersona: selectedPersona ?? this.selectedPersona,
    );
  }
}

class PersonasError extends PersonasState {
  final String message;

  const PersonasError(this.message);

  @override
  List<Object?> get props => [message];
}

class PersonaCreating extends PersonasState {}

class PersonaCreated extends PersonasState {
  final AIPersona persona;

  const PersonaCreated(this.persona);

  @override
  List<Object?> get props => [persona];
}

class PersonaUpdating extends PersonasState {}

class PersonaUpdated extends PersonasState {
  final AIPersona persona;

  const PersonaUpdated(this.persona);

  @override
  List<Object?> get props => [persona];
}

class PersonaDeleting extends PersonasState {}

class PersonaDeleted extends PersonasState {
  final String personaId;

  const PersonaDeleted(this.personaId);

  @override
  List<Object?> get props => [personaId];
}

class PersonaSuggestions extends PersonasState {
  final List<AIPersona> suggestions;
  final String content;

  const PersonaSuggestions(this.suggestions, this.content);

  @override
  List<Object?> get props => [suggestions, content];
}

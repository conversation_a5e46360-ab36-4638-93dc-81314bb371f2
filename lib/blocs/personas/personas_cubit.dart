import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive/hive.dart';
import '../../data/repositories/repositories.dart';
import '../../models/models.dart';
import 'personas_state.dart';

class PersonasCubit extends Cubit<PersonasState> {
  final PersonaRepository _personaRepository;
  StreamSubscription<BoxEvent>? _personasSubscription;

  PersonasCubit({required PersonaRepository personaRepository})
      : _personaRepository = personaRepository,
        super(PersonasInitial());

  @override
  Future<void> close() {
    _personasSubscription?.cancel();
    return super.close();
  }

  Future<void> loadPersonas() async {
    try {
      emit(PersonasLoading());
      
      final personas = await _personaRepository.getAllPersonas();
      final defaultPersona = await _personaRepository.getDefaultPersona();
      
      emit(PersonasLoaded(personas, selectedPersona: defaultPersona));
      
      // Watch for changes
      _personasSubscription?.cancel();
      _personasSubscription = _personaRepository.watchPersonas().listen((_) {
        _reloadPersonas();
      });
    } catch (e) {
      emit(PersonasError(e.toString()));
    }
  }

  Future<void> _reloadPersonas() async {
    try {
      final personas = await _personaRepository.getAllPersonas();
      final currentState = state;
      
      if (currentState is PersonasLoaded) {
        emit(currentState.copyWith(personas: personas));
      } else {
        final defaultPersona = await _personaRepository.getDefaultPersona();
        emit(PersonasLoaded(personas, selectedPersona: defaultPersona));
      }
    } catch (e) {
      emit(PersonasError(e.toString()));
    }
  }

  Future<void> selectPersona(AIPersona persona) async {
    final currentState = state;
    if (currentState is PersonasLoaded) {
      emit(currentState.copyWith(selectedPersona: persona));
    }
  }

  Future<void> createPersona({
    required String name,
    required String description,
    required String systemPrompt,
    required String avatar,
    required List<String> specialties,
  }) async {
    try {
      emit(PersonaCreating());
      
      final persona = await _personaRepository.createCustomPersona(
        name: name,
        description: description,
        systemPrompt: systemPrompt,
        avatar: avatar,
        specialties: specialties,
      );
      
      emit(PersonaCreated(persona));
      
      // Reload personas to show the updated state
      await loadPersonas();
    } catch (e) {
      emit(PersonasError(e.toString()));
    }
  }

  Future<void> updatePersona(AIPersona persona) async {
    try {
      emit(PersonaUpdating());
      
      await _personaRepository.updatePersona(persona);
      
      emit(PersonaUpdated(persona));
      
      // Reload will be triggered by the watch stream
    } catch (e) {
      emit(PersonasError(e.toString()));
    }
  }

  Future<void> deletePersona(String personaId) async {
    try {
      emit(PersonaDeleting());
      
      await _personaRepository.deletePersona(personaId);
      
      emit(PersonaDeleted(personaId));
      
      // Reload will be triggered by the watch stream
    } catch (e) {
      emit(PersonasError(e.toString()));
    }
  }

  Future<void> suggestPersonasForContent(String content) async {
    try {
      final suggestions = await _personaRepository.suggestPersonasForContent(content);
      emit(PersonaSuggestions(suggestions, content));
    } catch (e) {
      emit(PersonasError(e.toString()));
    }
  }

  Future<List<AIPersona>> getBuiltInPersonas() async {
    return await _personaRepository.getBuiltInPersonas();
  }

  Future<List<AIPersona>> getCustomPersonas() async {
    return await _personaRepository.getCustomPersonas();
  }

  Future<AIPersona?> getPersonaById(String id) async {
    return await _personaRepository.getPersona(id);
  }
}

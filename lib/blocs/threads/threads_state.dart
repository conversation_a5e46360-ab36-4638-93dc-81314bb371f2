import 'package:equatable/equatable.dart';
import '../../models/models.dart';

abstract class ThreadsState extends Equatable {
  const ThreadsState();

  @override
  List<Object?> get props => [];
}

class ThreadsInitial extends ThreadsState {}

class ThreadsLoading extends ThreadsState {}

class ThreadsLoaded extends ThreadsState {
  final List<Thread> threads;
  final String? currentSpaceId;

  const ThreadsLoaded(this.threads, {this.currentSpaceId});

  @override
  List<Object?> get props => [threads, currentSpaceId];
}

class ThreadsError extends ThreadsState {
  final String message;

  const ThreadsError(this.message);

  @override
  List<Object?> get props => [message];
}

class ThreadCreating extends ThreadsState {}

class ThreadCreated extends ThreadsState {
  final Thread thread;

  const ThreadCreated(this.thread);

  @override
  List<Object?> get props => [thread];
}
import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive/hive.dart';
import '../../data/repositories/repositories.dart';
import '../../models/models.dart';
import 'threads_state.dart';

class ThreadsCubit extends Cubit<ThreadsState> {
  final ThreadRepository _threadRepository;
  final SpaceRepository _spaceRepository;
  StreamSubscription<BoxEvent>? _threadsSubscription;
  String? _currentSpaceId;

  ThreadsCubit({
    required ThreadRepository threadRepository,
    required SpaceRepository spaceRepository,
  })  : _threadRepository = threadRepository,
        _spaceRepository = spaceRepository,
        super(ThreadsInitial()) {
    _watchThreads();
  }

  void _watchThreads() {
    _threadsSubscription = _threadRepository.watchThreads().listen((_) {
      if (_currentSpaceId != null) {
        loadThreadsForSpace(_currentSpaceId!);
      }
    });
  }

  Future<void> loadThreadsForSpace(String spaceId) async {
    try {
      emit(ThreadsLoading());
      _currentSpaceId = spaceId;
      final threads = await _threadRepository.getThreadsForSpace(spaceId);
      emit(ThreadsLoaded(threads, currentSpaceId: spaceId));
    } catch (e) {
      emit(ThreadsError(e.toString()));
    }
  }

  Future<void> createThread({
    required String spaceId,
    required String title,
    required String modelId,
    String? personaId,
  }) async {
    try {
      emit(ThreadCreating());
      final thread = await _threadRepository.createThread(
        spaceId: spaceId,
        title: title,
        modelId: modelId,
        personaId: personaId,
      );
      
      // Add thread to space
      await _spaceRepository.addThreadToSpace(spaceId, thread.id);
      
      emit(ThreadCreated(thread));
      // Reload threads to show the updated state
      await loadThreadsForSpace(spaceId);
    } catch (e) {
      emit(ThreadsError(e.toString()));
    }
  }

  Future<void> updateThread(Thread thread) async {
    try {
      await _threadRepository.updateThread(thread);
      // loadThreadsForSpace will be called automatically by the watch stream
    } catch (e) {
      emit(ThreadsError(e.toString()));
    }
  }

  Future<void> deleteThread(String threadId) async {
    try {
      final thread = await _threadRepository.getThread(threadId);
      if (thread != null) {
        // Remove thread from space
        await _spaceRepository.removeThreadFromSpace(thread.spaceId, threadId);
        // Delete the thread
        await _threadRepository.deleteThread(threadId);
      }
      // loadThreadsForSpace will be called automatically by the watch stream
    } catch (e) {
      emit(ThreadsError(e.toString()));
    }
  }

  Future<void> addMessageToThread(String threadId, Message message) async {
    try {
      await _threadRepository.addMessageToThread(threadId, message);
      // Thread update will trigger the watch stream automatically
    } catch (e) {
      emit(ThreadsError(e.toString()));
    }
  }

  @override
  Future<void> close() {
    _threadsSubscription?.cancel();
    return super.close();
  }
}
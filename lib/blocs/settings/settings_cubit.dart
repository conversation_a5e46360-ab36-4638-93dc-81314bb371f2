import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../models/models.dart';
import '../../data/hive_storage.dart';

part 'settings_state.dart';

class SettingsCubit extends Cubit<SettingsState> {
  SettingsCubit() : super(const SettingsState.loading());

  /// Load all settings from storage
  Future<void> loadSettings() async {
    try {
      final aiProviders = await HiveStorage.getAIProviders();
      final mcpServers = await HiveStorage.getMCPServers();
      final appSettings = await _loadAppSettings();

      emit(SettingsState.loaded(
        aiProviders: aiProviders,
        mcpServers: mcpServers,
        appSettings: appSettings,
      ));
    } catch (e) {
      emit(SettingsState.error(e.toString()));
    }
  }

  /// Save AI provider
  Future<void> saveAIProvider(AIProvider provider) async {
    try {
      await HiveStorage.saveAIProvider(provider);
      
      if (state is SettingsLoaded) {
        final currentState = state as SettingsLoaded;
        final updatedProviders = Map<String, AIProvider>.from(currentState.aiProviders);
        updatedProviders[provider.id] = provider;
        
        emit(currentState.copyWith(aiProviders: updatedProviders));
      }
    } catch (e) {
      emit(SettingsState.error(e.toString()));
    }
  }

  /// Delete AI provider
  Future<void> deleteAIProvider(String providerId) async {
    try {
      await HiveStorage.deleteAIProvider(providerId);
      
      if (state is SettingsLoaded) {
        final currentState = state as SettingsLoaded;
        final updatedProviders = Map<String, AIProvider>.from(currentState.aiProviders);
        updatedProviders.remove(providerId);
        
        emit(currentState.copyWith(aiProviders: updatedProviders));
      }
    } catch (e) {
      emit(SettingsState.error(e.toString()));
    }
  }

  /// Save MCP server
  Future<void> saveMCPServer(MCPServer server) async {
    try {
      await HiveStorage.saveMCPServer(server);
      
      if (state is SettingsLoaded) {
        final currentState = state as SettingsLoaded;
        final updatedServers = Map<String, MCPServer>.from(currentState.mcpServers);
        updatedServers[server.id] = server;
        
        emit(currentState.copyWith(mcpServers: updatedServers));
      }
    } catch (e) {
      emit(SettingsState.error(e.toString()));
    }
  }

  /// Delete MCP server
  Future<void> deleteMCPServer(String serverId) async {
    try {
      await HiveStorage.deleteMCPServer(serverId);
      
      if (state is SettingsLoaded) {
        final currentState = state as SettingsLoaded;
        final updatedServers = Map<String, MCPServer>.from(currentState.mcpServers);
        updatedServers.remove(serverId);
        
        emit(currentState.copyWith(mcpServers: updatedServers));
      }
    } catch (e) {
      emit(SettingsState.error(e.toString()));
    }
  }

  /// Update app settings
  Future<void> updateAppSettings(AppSettings settings) async {
    try {
      await _saveAppSettings(settings);
      
      if (state is SettingsLoaded) {
        final currentState = state as SettingsLoaded;
        emit(currentState.copyWith(appSettings: settings));
      }
    } catch (e) {
      emit(SettingsState.error(e.toString()));
    }
  }

  /// Load app settings from storage
  Future<AppSettings> _loadAppSettings() async {
    final box = await HiveStorage.getSettingsBox();
    return AppSettings(
      theme: box.get('theme', defaultValue: 'system'),
      language: box.get('language', defaultValue: 'en'),
      defaultModel: box.get('defaultModel'),
      enableNotifications: box.get('enableNotifications', defaultValue: true),
      autoSave: box.get('autoSave', defaultValue: true),
      maxTokens: box.get('maxTokens', defaultValue: 4096),
      temperature: box.get('temperature', defaultValue: 0.7),
    );
  }

  /// Save app settings to storage
  Future<void> _saveAppSettings(AppSettings settings) async {
    final box = await HiveStorage.getSettingsBox();
    await box.putAll({
      'theme': settings.theme,
      'language': settings.language,
      'defaultModel': settings.defaultModel,
      'enableNotifications': settings.enableNotifications,
      'autoSave': settings.autoSave,
      'maxTokens': settings.maxTokens,
      'temperature': settings.temperature,
    });
  }
}

/// App-level settings
class AppSettings {
  final String theme;
  final String language;
  final String? defaultModel;
  final bool enableNotifications;
  final bool autoSave;
  final int maxTokens;
  final double temperature;

  const AppSettings({
    required this.theme,
    required this.language,
    this.defaultModel,
    required this.enableNotifications,
    required this.autoSave,
    required this.maxTokens,
    required this.temperature,
  });

  AppSettings copyWith({
    String? theme,
    String? language,
    String? defaultModel,
    bool? enableNotifications,
    bool? autoSave,
    int? maxTokens,
    double? temperature,
  }) {
    return AppSettings(
      theme: theme ?? this.theme,
      language: language ?? this.language,
      defaultModel: defaultModel ?? this.defaultModel,
      enableNotifications: enableNotifications ?? this.enableNotifications,
      autoSave: autoSave ?? this.autoSave,
      maxTokens: maxTokens ?? this.maxTokens,
      temperature: temperature ?? this.temperature,
    );
  }
}
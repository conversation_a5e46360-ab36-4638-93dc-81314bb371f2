part of 'settings_cubit.dart';

abstract class SettingsState extends Equatable {
  const SettingsState();

  const factory SettingsState.loading() = SettingsLoading;
  const factory SettingsState.loaded({
    required Map<String, AIProvider> aiProviders,
    required Map<String, MCPServer> mcpServers,
    required AppSettings appSettings,
  }) = SettingsLoaded;
  const factory SettingsState.error(String message) = SettingsError;

  @override
  List<Object?> get props => [];
}

class SettingsLoading extends SettingsState {
  const SettingsLoading();
}

class SettingsLoaded extends SettingsState {
  final Map<String, AIProvider> aiProviders;
  final Map<String, MCPServer> mcpServers;
  final AppSettings appSettings;

  const SettingsLoaded({
    required this.aiProviders,
    required this.mcpServers,
    required this.appSettings,
  });

  SettingsLoaded copyWith({
    Map<String, AIProvider>? aiProviders,
    Map<String, MCPServer>? mcpServers,
    AppSettings? appSettings,
  }) {
    return SettingsLoaded(
      aiProviders: aiProviders ?? this.aiProviders,
      mcpServers: mcpServers ?? this.mcpServers,
      appSettings: appSettings ?? this.appSettings,
    );
  }

  @override
  List<Object> get props => [aiProviders, mcpServers, appSettings];
}

class SettingsError extends SettingsState {
  final String message;

  const SettingsError(this.message);

  @override
  List<Object> get props => [message];
}
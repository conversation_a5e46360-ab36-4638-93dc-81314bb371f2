import 'package:flutter/material.dart';

class StaggeredAnimatedGrid extends StatefulWidget {
  final int itemCount;
  final Widget Function(BuildContext context, int index) itemBuilder;
  final SliverGridDelegate gridDelegate;
  final EdgeInsetsGeometry? padding;

  const StaggeredAnimatedGrid({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    required this.gridDelegate,
    this.padding,
  });

  @override
  State<StaggeredAnimatedGrid> createState() => _StaggeredAnimatedGridState();
}

class _StaggeredAnimatedGridState extends State<StaggeredAnimatedGrid>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  final List<AnimationController> _itemControllers = [];
  final List<Animation<double>> _itemAnimations = [];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _setupAnimations();
    _animationController.forward();
  }

  void _setupAnimations() {
    for (int i = 0; i < widget.itemCount; i++) {
      final controller = AnimationController(
        duration: const Duration(milliseconds: 300),
        vsync: this,
      );
      final animation = Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.easeOutBack,
      ));

      _itemControllers.add(controller);
      _itemAnimations.add(animation);

      // Stagger the animations
      Future.delayed(Duration(milliseconds: i * 100), () {
        if (mounted) {
          controller.forward();
        }
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    for (final controller in _itemControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: widget.padding ?? EdgeInsets.zero,
      child: GridView.builder(
        gridDelegate: widget.gridDelegate,
        itemCount: widget.itemCount,
        itemBuilder: (context, index) {
          if (index >= _itemAnimations.length) {
            return widget.itemBuilder(context, index);
          }

          return AnimatedBuilder(
            animation: _itemAnimations[index],
            builder: (context, child) {
              return Transform.scale(
                scale: _itemAnimations[index].value,
                child: Transform.translate(
                  offset: Offset(
                    0,
                    50 * (1 - _itemAnimations[index].value),
                  ),
                  child: Opacity(
                    opacity: _itemAnimations[index].value,
                    child: widget.itemBuilder(context, index),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
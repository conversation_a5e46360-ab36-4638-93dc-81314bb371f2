import 'package:flutter/material.dart';

class SlidePageRoute<T> extends PageRouteBuilder<T> {
  final Widget page;
  final AxisDirection direction;

  SlidePageRoute({
    required this.page,
    this.direction = AxisDirection.left,
    super.settings,
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: const Duration(milliseconds: 300),
          reverseTransitionDuration: const Duration(milliseconds: 250),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            Offset begin;
            const end = Offset.zero;
            
            switch (direction) {
              case AxisDirection.up:
                begin = const Offset(0.0, 1.0);
                break;
              case AxisDirection.down:
                begin = const Offset(0.0, -1.0);
                break;
              case AxisDirection.left:
                begin = const Offset(1.0, 0.0);
                break;
              case AxisDirection.right:
                begin = const Offset(-1.0, 0.0);
                break;
            }

            final tween = Tween(begin: begin, end: end);
            final offsetAnimation = animation.drive(tween.chain(
              CurveTween(curve: Curves.easeInOutCubic),
            ));

            return SlideTransition(
              position: offsetAnimation,
              child: FadeTransition(
                opacity: animation.drive(
                  Tween(begin: 0.0, end: 1.0).chain(
                    CurveTween(curve: Curves.easeIn),
                  ),
                ),
                child: child,
              ),
            );
          },
        );
}

class ScalePageRoute<T> extends PageRouteBuilder<T> {
  final Widget page;

  ScalePageRoute({
    required this.page,
    super.settings,
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: const Duration(milliseconds: 400),
          reverseTransitionDuration: const Duration(milliseconds: 350),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = 0.0;
            const end = 1.0;
            const curve = Curves.easeInOutCubic;

            final tween = Tween(begin: begin, end: end);
            final curvedAnimation = CurvedAnimation(
              parent: animation,
              curve: curve,
            );

            return ScaleTransition(
              scale: tween.animate(curvedAnimation),
              child: FadeTransition(
                opacity: animation,
                child: child,
              ),
            );
          },
        );
}

class FadePageRoute<T> extends PageRouteBuilder<T> {
  final Widget page;

  FadePageRoute({
    required this.page,
    super.settings,
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: const Duration(milliseconds: 300),
          reverseTransitionDuration: const Duration(milliseconds: 250),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: animation.drive(
                Tween(begin: 0.0, end: 1.0).chain(
                  CurveTween(curve: Curves.easeInOut),
                ),
              ),
              child: child,
            );
          },
        );
}

class RotationPageRoute<T> extends PageRouteBuilder<T> {
  final Widget page;

  RotationPageRoute({
    required this.page,
    super.settings,
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: const Duration(milliseconds: 500),
          reverseTransitionDuration: const Duration(milliseconds: 400),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return RotationTransition(
              turns: animation.drive(
                Tween(begin: 0.0, end: 1.0).chain(
                  CurveTween(curve: Curves.easeInOutCubic),
                ),
              ),
              child: FadeTransition(
                opacity: animation,
                child: child,
              ),
            );
          },
        );
}

// Extension methods for easy navigation
extension NavigationExtensions on NavigatorState {
  Future<T?> pushSlide<T extends Object?>(
    Widget page, {
    AxisDirection direction = AxisDirection.left,
    RouteSettings? settings,
  }) {
    return push<T>(SlidePageRoute<T>(
      page: page,
      direction: direction,
      settings: settings,
    ));
  }

  Future<T?> pushScale<T extends Object?>(
    Widget page, {
    RouteSettings? settings,
  }) {
    return push<T>(ScalePageRoute<T>(
      page: page,
      settings: settings,
    ));
  }

  Future<T?> pushFade<T extends Object?>(
    Widget page, {
    RouteSettings? settings,
  }) {
    return push<T>(FadePageRoute<T>(
      page: page,
      settings: settings,
    ));
  }

  Future<T?> pushRotation<T extends Object?>(
    Widget page, {
    RouteSettings? settings,
  }) {
    return push<T>(RotationPageRoute<T>(
      page: page,
      settings: settings,
    ));
  }
}

// Context extension for easier access
extension BuildContextNavigationExtensions on BuildContext {
  Future<T?> pushSlide<T extends Object?>(
    Widget page, {
    AxisDirection direction = AxisDirection.left,
    RouteSettings? settings,
  }) {
    return Navigator.of(this).pushSlide<T>(
      page,
      direction: direction,
      settings: settings,
    );
  }

  Future<T?> pushScale<T extends Object?>(
    Widget page, {
    RouteSettings? settings,
  }) {
    return Navigator.of(this).pushScale<T>(page, settings: settings);
  }

  Future<T?> pushFade<T extends Object?>(
    Widget page, {
    RouteSettings? settings,
  }) {
    return Navigator.of(this).pushFade<T>(page, settings: settings);
  }

  Future<T?> pushRotation<T extends Object?>(
    Widget page, {
    RouteSettings? settings,
  }) {
    return Navigator.of(this).pushRotation<T>(page, settings: settings);
  }
}
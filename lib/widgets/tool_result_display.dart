import 'package:flutter/material.dart';
import '../models/tool_execution.dart';

class ToolResultDisplay extends StatelessWidget {
  final List<ToolExecution> toolExecutions;

  const ToolResultDisplay({
    super.key,
    required this.toolExecutions,
  });

  @override
  Widget build(BuildContext context) {
    if (toolExecutions.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(top: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.extension,
                  size: 16,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Tool Results',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
          ...toolExecutions.map((execution) => _buildToolResult(context, execution)),
        ],
      ),
    );
  }

  Widget _buildToolResult(BuildContext context, ToolExecution execution) {
    final toolName = execution.toolId.contains(':') 
        ? execution.toolId.split(':').last 
        : execution.toolId;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getStatusIcon(execution.status),
                size: 16,
                color: _getStatusColor(context, execution.status),
              ),
              const SizedBox(width: 8),
              Text(
                toolName,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Text(
                execution.status.name.toUpperCase(),
                style: TextStyle(
                  fontSize: 12,
                  color: _getStatusColor(context, execution.status),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          if (execution.status == ExecutionStatus.completed && execution.result != null) ...[
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                ),
              ),
              child: Text(
                execution.result!,
                style: const TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 12,
                ),
              ),
            ),
          ],
          if (execution.status == ExecutionStatus.failed && execution.errorMessage != null) ...[
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.errorContainer,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                execution.errorMessage!,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onErrorContainer,
                  fontSize: 12,
                ),
              ),
            ),
          ],
          const SizedBox(height: 4),
          Text(
            'Executed at ${_formatTimestamp(execution.timestamp)}',
            style: TextStyle(
              fontSize: 10,
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getStatusIcon(ExecutionStatus status) {
    switch (status) {
      case ExecutionStatus.pending:
        return Icons.schedule;
      case ExecutionStatus.running:
        return Icons.refresh;
      case ExecutionStatus.completed:
        return Icons.check_circle;
      case ExecutionStatus.failed:
        return Icons.error;
    }
  }

  Color _getStatusColor(BuildContext context, ExecutionStatus status) {
    switch (status) {
      case ExecutionStatus.pending:
        return Theme.of(context).colorScheme.outline;
      case ExecutionStatus.running:
        return Theme.of(context).colorScheme.primary;
      case ExecutionStatus.completed:
        return Colors.green;
      case ExecutionStatus.failed:
        return Theme.of(context).colorScheme.error;
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inSeconds < 60) {
      return 'just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }
}

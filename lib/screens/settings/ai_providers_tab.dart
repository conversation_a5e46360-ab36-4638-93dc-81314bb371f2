import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../blocs/settings/settings_cubit.dart';
import '../../models/models.dart';
import '../../widgets/widgets.dart';
import 'ai_provider_dialog.dart';

class AIProvidersTab extends StatelessWidget {
  const AIProvidersTab({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SettingsCubit, SettingsState>(
      builder: (context, state) {
        if (state is SettingsLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is SettingsError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Theme.of(context).colorScheme.error,
                ),
                const SizedBox(height: 16),
                Text(
                  'Error loading settings',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(state.message),
                const SizedBox(height: 16),
                FilledButton(
                  onPressed: () => context.read<SettingsCubit>().loadSettings(),
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        if (state is SettingsLoaded) {
          final providers = state.aiProviders.values.toList();

          return Column(
            children: [
              // Header with add button
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Icon(
                      Icons.psychology,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'AI Providers',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    FilledButton.icon(
                      onPressed: () => _showProviderDialog(context),
                      icon: const Icon(Icons.add),
                      label: const Text('Add Provider'),
                    ),
                  ],
                ),
              ),

              // Quick add section for popular providers
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16.0),
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Quick Setup',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Add popular AI providers with pre-configured settings',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        _buildQuickAddChip(context, providers, 'openrouter', 'OpenRouter', Icons.router),
                        _buildQuickAddChip(context, providers, 'openai', 'OpenAI', Icons.smart_toy),
                        _buildQuickAddChip(context, providers, 'anthropic', 'Anthropic', Icons.psychology),
                        _buildQuickAddChip(context, providers, 'ollama', 'Ollama', Icons.computer),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              if (providers.isEmpty)
                Expanded(
                  child: EmptyState(
                    icon: Icons.psychology_outlined,
                    title: 'No AI Providers',
                    subtitle: 'Add AI providers to start chatting with different models',
                    actionText: 'Add Provider',
                    onAction: () => _showProviderDialog(context),
                  ),
                )
              else
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    itemCount: providers.length,
                    itemBuilder: (context, index) {
                      final provider = providers[index];
                      return _ProviderCard(
                        provider: provider,
                        onEdit: () => _showProviderDialog(context, provider: provider),
                        onDelete: () => _deleteProvider(context, provider),
                      );
                    },
                  ),
                ),
            ],
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  void _showProviderDialog(BuildContext context, {AIProvider? provider}) {
    showDialog(
      context: context,
      builder: (context) => AIProviderDialog(provider: provider),
    );
  }

  void _quickAddProvider(BuildContext context, String type) {
    final state = context.read<SettingsCubit>().state;
    
    // Check if provider of this type already exists
    if (state is SettingsLoaded) {
      final existingProvider = state.aiProviders.values
          .cast<AIProvider?>()
          .firstWhere((p) => p?.type == type, orElse: () => null);
      
      if (existingProvider != null) {
        // Edit existing provider
        _showProviderDialog(context, provider: existingProvider);
        return;
      }
    }
    
    AIProvider? template;
    
    switch (type) {
      case 'openrouter':
        template = AIProvider(
          id: 'openrouter_${DateTime.now().millisecondsSinceEpoch}',
          name: 'OpenRouter',
          type: 'openrouter',
          baseUrl: 'https://openrouter.ai/api/v1',
          apiKey: '',
          models: const [],
          defaultModel: 'meta-llama/llama-3.1-8b-instruct:free',
          createdAt: DateTime.now(),
        );
        break;
      case 'openai':
        template = AIProvider(
          id: 'openai_${DateTime.now().millisecondsSinceEpoch}',
          name: 'OpenAI',
          type: 'openai',
          baseUrl: 'https://api.openai.com/v1',
          apiKey: '',
          models: const [],
          defaultModel: 'gpt-4o-mini',
          createdAt: DateTime.now(),
        );
        break;
      case 'anthropic':
        template = AIProvider(
          id: 'anthropic_${DateTime.now().millisecondsSinceEpoch}',
          name: 'Anthropic',
          type: 'anthropic',
          baseUrl: 'https://api.anthropic.com/v1',
          apiKey: '',
          models: const [],
          defaultModel: 'claude-3-haiku-20240307',
          createdAt: DateTime.now(),
        );
        break;
      case 'ollama':
        template = AIProvider(
          id: 'ollama_${DateTime.now().millisecondsSinceEpoch}',
          name: 'Ollama',
          type: 'ollama',
          baseUrl: 'http://localhost:11434/v1',
          apiKey: '',
          models: const [],
          defaultModel: 'llama3.2:3b',
          createdAt: DateTime.now(),
        );
        break;
    }

    if (template != null) {
      _showProviderDialog(context, provider: template);
    }
  }

  void _deleteProvider(BuildContext context, AIProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Provider'),
        content: Text('Are you sure you want to delete "${provider.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              context.read<SettingsCubit>().deleteAIProvider(provider.id);
              Navigator.of(context).pop();
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickAddChip(BuildContext context, List<AIProvider> providers, String type, String label, IconData icon) {
    final existingProvider = providers.cast<AIProvider?>().firstWhere((p) => p?.type == type, orElse: () => null);
    final bool exists = existingProvider != null;
    
    return _QuickAddChip(
      label: exists ? '$label (Edit)' : label,
      icon: icon,
      exists: exists,
      onTap: () => _quickAddProvider(context, type),
    );
  }
}

class _QuickAddChip extends StatelessWidget {
  final String label;
  final IconData icon;
  final VoidCallback onTap;
  final bool exists;

  const _QuickAddChip({
    required this.label,
    required this.icon,
    required this.onTap,
    this.exists = false,
  });

  @override
  Widget build(BuildContext context) {
    return ActionChip(
      avatar: Icon(
        exists ? Icons.edit : Icons.add,
        size: 18,
      ),
      label: Text(label),
      onPressed: onTap,
      backgroundColor: exists 
          ? Theme.of(context).colorScheme.secondaryContainer
          : null,
    );
  }
}

class _ProviderCard extends StatelessWidget {
  final AIProvider provider;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const _ProviderCard({
    required this.provider,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Provider icon
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getProviderIcon(provider.type),
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
                ),
                const SizedBox(width: 12),
                
                // Provider info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        provider.name,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        provider.type.toUpperCase(),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Status and actions
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // API key status
                    Icon(
                      provider.apiKey.isEmpty 
                          ? Icons.key_off 
                          : Icons.key,
                      size: 20,
                      color: provider.apiKey.isEmpty 
                          ? Theme.of(context).colorScheme.error 
                          : Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    
                    PopupMenuButton<String>(
                      onSelected: (value) {
                        switch (value) {
                          case 'edit':
                            onEdit();
                            break;
                          case 'delete':
                            onDelete();
                            break;
                        }
                      },
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'edit',
                          child: ListTile(
                            leading: Icon(Icons.edit),
                            title: Text('Edit'),
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: ListTile(
                            leading: Icon(Icons.delete),
                            title: Text('Delete'),
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Provider details
            Container(
              padding: const EdgeInsets.all(12.0),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _InfoRow(
                    icon: Icons.link,
                    label: 'Base URL',
                    value: provider.baseUrl ?? 'Not set',
                  ),
                  _InfoRow(
                    icon: Icons.model_training,
                    label: 'Default Model',
                    value: provider.defaultModel ?? 'Not set',
                  ),
                  _InfoRow(
                    icon: Icons.list,
                    label: 'Models',
                    value: provider.models.isEmpty 
                        ? 'None configured' 
                        : '${provider.models.length} models',
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getProviderIcon(String type) {
    switch (type.toLowerCase()) {
      case 'openrouter':
        return Icons.router;
      case 'openai':
        return Icons.smart_toy;
      case 'anthropic':
        return Icons.psychology;
      case 'ollama':
        return Icons.computer;
      default:
        return Icons.cloud;
    }
  }
}

class _InfoRow extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;

  const _InfoRow({
    required this.icon,
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 16,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: RichText(
              text: TextSpan(
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                children: [
                  TextSpan(
                    text: '$label: ',
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  TextSpan(text: value),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
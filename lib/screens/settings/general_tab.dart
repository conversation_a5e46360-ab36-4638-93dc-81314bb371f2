import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../blocs/settings/settings_cubit.dart';

class GeneralTab extends StatelessWidget {
  const GeneralTab({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SettingsCubit, SettingsState>(
      builder: (context, state) {
        if (state is SettingsLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is SettingsError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Theme.of(context).colorScheme.error,
                ),
                const SizedBox(height: 16),
                Text(
                  'Error loading settings',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(state.message),
                const SizedBox(height: 16),
                FilledButton(
                  onPressed: () => context.read<SettingsCubit>().loadSettings(),
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        if (state is SettingsLoaded) {
          final settings = state.appSettings;

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // App Appearance
                _SettingsSection(
                  title: 'Appearance',
                  icon: Icons.palette,
                  children: [
                    _SettingsTile(
                      title: 'Theme',
                      subtitle: 'Choose your preferred theme',
                      child: SegmentedButton<String>(
                        segments: const [
                          ButtonSegment(
                            value: 'system',
                            label: Text('System'),
                            icon: Icon(Icons.brightness_auto),
                          ),
                          ButtonSegment(
                            value: 'light',
                            label: Text('Light'),
                            icon: Icon(Icons.light_mode),
                          ),
                          ButtonSegment(
                            value: 'dark',
                            label: Text('Dark'),
                            icon: Icon(Icons.dark_mode),
                          ),
                        ],
                        selected: {settings.theme},
                        onSelectionChanged: (values) {
                          final theme = values.first;
                          context.read<SettingsCubit>().updateAppSettings(
                            settings.copyWith(theme: theme),
                          );
                        },
                      ),
                    ),
                    _SettingsTile(
                      title: 'Language',
                      subtitle: 'Choose your preferred language',
                      child: DropdownButton<String>(
                        value: settings.language,
                        items: const [
                          DropdownMenuItem(
                            value: 'en',
                            child: Text('English'),
                          ),
                          DropdownMenuItem(
                            value: 'es',
                            child: Text('Español'),
                          ),
                          DropdownMenuItem(
                            value: 'fr',
                            child: Text('Français'),
                          ),
                          DropdownMenuItem(
                            value: 'de',
                            child: Text('Deutsch'),
                          ),
                        ],
                        onChanged: (value) {
                          if (value != null) {
                            context.read<SettingsCubit>().updateAppSettings(
                              settings.copyWith(language: value),
                            );
                          }
                        },
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Chat Settings
                _SettingsSection(
                  title: 'Chat Settings',
                  icon: Icons.chat,
                  children: [
                    _SettingsTile(
                      title: 'Auto-save conversations',
                      subtitle: 'Automatically save your conversations',
                      child: Switch(
                        value: settings.autoSave,
                        onChanged: (value) {
                          context.read<SettingsCubit>().updateAppSettings(
                            settings.copyWith(autoSave: value),
                          );
                        },
                      ),
                    ),
                    _SettingsTile(
                      title: 'Max Tokens',
                      subtitle: 'Maximum tokens for AI responses (${settings.maxTokens})',
                      child: SizedBox(
                        width: 200,
                        child: Slider(
                          value: settings.maxTokens.toDouble(),
                          min: 100,
                          max: 8192,
                          divisions: 81,
                          label: settings.maxTokens.toString(),
                          onChanged: (value) {
                            context.read<SettingsCubit>().updateAppSettings(
                              settings.copyWith(maxTokens: value.round()),
                            );
                          },
                        ),
                      ),
                    ),
                    _SettingsTile(
                      title: 'Temperature',
                      subtitle: 'Creativity level for AI responses (${settings.temperature.toStringAsFixed(1)})',
                      child: SizedBox(
                        width: 200,
                        child: Slider(
                          value: settings.temperature,
                          min: 0.0,
                          max: 2.0,
                          divisions: 20,
                          label: settings.temperature.toStringAsFixed(1),
                          onChanged: (value) {
                            context.read<SettingsCubit>().updateAppSettings(
                              settings.copyWith(temperature: value),
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Notifications
                _SettingsSection(
                  title: 'Notifications',
                  icon: Icons.notifications,
                  children: [
                    _SettingsTile(
                      title: 'Enable notifications',
                      subtitle: 'Get notified about important events',
                      child: Switch(
                        value: settings.enableNotifications,
                        onChanged: (value) {
                          context.read<SettingsCubit>().updateAppSettings(
                            settings.copyWith(enableNotifications: value),
                          );
                        },
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // About Section
                _SettingsSection(
                  title: 'About',
                  icon: Icons.info,
                  children: [
                    ListTile(
                      title: const Text('Version'),
                      subtitle: const Text('1.0.0'),
                      leading: const Icon(Icons.apps),
                      contentPadding: EdgeInsets.zero,
                    ),
                    ListTile(
                      title: const Text('Build'),
                      subtitle: const Text('Debug Build'),
                      leading: const Icon(Icons.build),
                      contentPadding: EdgeInsets.zero,
                    ),
                    ListTile(
                      title: const Text('Privacy Policy'),
                      subtitle: const Text('View our privacy policy'),
                      leading: const Icon(Icons.privacy_tip),
                      trailing: const Icon(Icons.chevron_right),
                      contentPadding: EdgeInsets.zero,
                      onTap: () {
                        // TODO: Open privacy policy
                      },
                    ),
                    ListTile(
                      title: const Text('Terms of Service'),
                      subtitle: const Text('View our terms of service'),
                      leading: const Icon(Icons.description),
                      trailing: const Icon(Icons.chevron_right),
                      contentPadding: EdgeInsets.zero,
                      onTap: () {
                        // TODO: Open terms of service
                      },
                    ),
                  ],
                ),
              ],
            ),
          );
        }

        return const SizedBox.shrink();
      },
    );
  }
}

class _SettingsSection extends StatelessWidget {
  final String title;
  final IconData icon;
  final List<Widget> children;

  const _SettingsSection({
    required this.title,
    required this.icon,
    required this.children,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: children,
            ),
          ),
        ),
      ],
    );
  }
}

class _SettingsTile extends StatelessWidget {
  final String title;
  final String subtitle;
  final Widget child;

  const _SettingsTile({
    required this.title,
    required this.subtitle,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          child,
        ],
      ),
    );
  }
}
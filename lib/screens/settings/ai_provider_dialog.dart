import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../blocs/settings/settings_cubit.dart';
import '../../models/models.dart';

class AIProviderDialog extends StatefulWidget {
  final AIProvider? provider;

  const AIProviderDialog({super.key, this.provider});

  @override
  State<AIProviderDialog> createState() => _AIProviderDialogState();
}

class _AIProviderDialogState extends State<AIProviderDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _typeController = TextEditingController();
  final _baseUrlController = TextEditingController();
  final _apiKeyController = TextEditingController();
  final _defaultModelController = TextEditingController();
  final _modelsController = TextEditingController();

  bool _obscureApiKey = true;

  final List<String> _providerTypes = [
    'openrouter',
    'openai',
    'anthropic',
    'ollama',
    'custom',
  ];

  @override
  void initState() {
    super.initState();
    if (widget.provider != null) {
      _populateFields(widget.provider!);
      // Show API key by default when editing existing provider
      if (widget.provider!.apiKey.isNotEmpty) {
        _obscureApiKey = false;
      }
    }
  }

  void _populateFields(AIProvider provider) {
    _nameController.text = provider.name;
    _typeController.text = provider.type;
    _baseUrlController.text = provider.baseUrl ?? '';
    _apiKeyController.text = provider.apiKey;
    _defaultModelController.text = provider.defaultModel ?? '';
    _modelsController.text = provider.models.join(', ');
  }

  @override
  void dispose() {
    _nameController.dispose();
    _typeController.dispose();
    _baseUrlController.dispose();
    _apiKeyController.dispose();
    _defaultModelController.dispose();
    _modelsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 500,
        constraints: const BoxConstraints(maxHeight: 600),
        padding: const EdgeInsets.all(24.0),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Text(
                widget.provider != null ? 'Edit AI Provider' : 'Add AI Provider',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 24),

              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Provider name
                      TextFormField(
                        controller: _nameController,
                        decoration: const InputDecoration(
                          labelText: 'Provider Name',
                          helperText: 'A friendly name for this provider',
                          prefixIcon: Icon(Icons.label),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter a provider name';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Provider type
                      DropdownButtonFormField<String>(
                        value: _typeController.text.isEmpty ? null : _typeController.text,
                        decoration: const InputDecoration(
                          labelText: 'Provider Type',
                          prefixIcon: Icon(Icons.category),
                        ),
                        items: _providerTypes
                            .map((type) => DropdownMenuItem(
                                  value: type,
                                  child: Text(type.toUpperCase()),
                                ))
                            .toList(),
                        onChanged: (value) {
                          setState(() {
                            _typeController.text = value ?? '';
                            _updateDefaultsForType(value ?? '');
                          });
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please select a provider type';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Base URL
                      TextFormField(
                        controller: _baseUrlController,
                        decoration: const InputDecoration(
                          labelText: 'Base URL',
                          helperText: 'API endpoint URL',
                          prefixIcon: Icon(Icons.language),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter a base URL';
                          }
                          try {
                            Uri.parse(value.trim());
                          } catch (e) {
                            return 'Please enter a valid URL';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // API Key
                      TextFormField(
                        controller: _apiKeyController,
                        decoration: InputDecoration(
                          labelText: 'API Key',
                          helperText: 'Your API key for this provider',
                          prefixIcon: const Icon(Icons.key),
                          suffixIcon: IconButton(
                            icon: Icon(
                              _obscureApiKey ? Icons.visibility : Icons.visibility_off,
                            ),
                            onPressed: () {
                              setState(() {
                                _obscureApiKey = !_obscureApiKey;
                              });
                            },
                          ),
                        ),
                        obscureText: _obscureApiKey,
                        validator: (value) {
                          if (_typeController.text != 'ollama' && 
                              (value == null || value.trim().isEmpty)) {
                            return 'Please enter an API key';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Default model
                      TextFormField(
                        controller: _defaultModelController,
                        decoration: const InputDecoration(
                          labelText: 'Default Model',
                          helperText: 'The default model to use',
                          prefixIcon: Icon(Icons.model_training),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Available models
                      TextFormField(
                        controller: _modelsController,
                        decoration: const InputDecoration(
                          labelText: 'Available Models (Optional)',
                          helperText: 'Comma-separated list of available models',
                          prefixIcon: Icon(Icons.list),
                        ),
                        maxLines: 3,
                      ),

                      // Help text for provider types
                      if (_typeController.text.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surfaceContainerHighest,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.info_outline,
                                    size: 16,
                                    color: Theme.of(context).colorScheme.primary,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    '${_typeController.text.toUpperCase()} Configuration',
                                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context).colorScheme.primary,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 4),
                              Text(
                                _getProviderHelpText(_typeController.text),
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Actions
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 16),
                  FilledButton(
                    onPressed: _saveProvider,
                    child: Text(widget.provider != null ? 'Update' : 'Add'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _updateDefaultsForType(String type) {
    switch (type) {
      case 'openrouter':
        if (_baseUrlController.text.isEmpty) {
          _baseUrlController.text = 'https://openrouter.ai/api/v1';
        }
        if (_defaultModelController.text.isEmpty) {
          _defaultModelController.text = 'meta-llama/llama-3.1-8b-instruct:free';
        }
        break;
      case 'openai':
        if (_baseUrlController.text.isEmpty) {
          _baseUrlController.text = 'https://api.openai.com/v1';
        }
        if (_defaultModelController.text.isEmpty) {
          _defaultModelController.text = 'gpt-4o-mini';
        }
        break;
      case 'anthropic':
        if (_baseUrlController.text.isEmpty) {
          _baseUrlController.text = 'https://api.anthropic.com/v1';
        }
        if (_defaultModelController.text.isEmpty) {
          _defaultModelController.text = 'claude-3-haiku-20240307';
        }
        break;
      case 'ollama':
        if (_baseUrlController.text.isEmpty) {
          _baseUrlController.text = 'http://localhost:11434/v1';
        }
        if (_defaultModelController.text.isEmpty) {
          _defaultModelController.text = 'llama3.2:3b';
        }
        break;
    }
  }

  String _getProviderHelpText(String type) {
    switch (type) {
      case 'openrouter':
        return 'OpenRouter provides access to multiple AI models through a single API. Get your API key from openrouter.ai';
      case 'openai':
        return 'OpenAI provides GPT models. Get your API key from platform.openai.com';
      case 'anthropic':
        return 'Anthropic provides Claude models. Get your API key from console.anthropic.com';
      case 'ollama':
        return 'Ollama runs models locally. Make sure Ollama is running on your machine. No API key required.';
      case 'custom':
        return 'Configure a custom OpenAI-compatible API endpoint';
      default:
        return '';
    }
  }

  void _saveProvider() {
    if (!_formKey.currentState!.validate()) return;

    final models = _modelsController.text.trim().isEmpty
        ? <String>[]
        : _modelsController.text
            .split(',')
            .map((model) => model.trim())
            .where((model) => model.isNotEmpty)
            .toList();

    // Preserve original API key if field is empty but original provider had one
    final apiKey = _apiKeyController.text.trim().isEmpty && widget.provider?.apiKey.isNotEmpty == true
        ? widget.provider!.apiKey
        : _apiKeyController.text.trim();

    final provider = AIProvider(
      id: widget.provider?.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
      name: _nameController.text.trim(),
      type: _typeController.text,
      baseUrl: _baseUrlController.text.trim(),
      apiKey: apiKey,
      defaultModel: _defaultModelController.text.trim().isEmpty 
          ? null 
          : _defaultModelController.text.trim(),
      models: models,
      createdAt: widget.provider?.createdAt ?? DateTime.now(),
      updatedAt: DateTime.now(),
    );
    
    context.read<SettingsCubit>().saveAIProvider(provider);
    Navigator.of(context).pop();
  }
}
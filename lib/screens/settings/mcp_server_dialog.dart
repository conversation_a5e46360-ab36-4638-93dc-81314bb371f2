import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../blocs/settings/settings_cubit.dart';
import '../../models/models.dart';

class MCPServerDialog extends StatefulWidget {
  final MCPServer? server;

  const MCPServerDialog({super.key, this.server});

  @override
  State<MCPServerDialog> createState() => _MCPServerDialogState();
}

class _MCPServerDialogState extends State<MCPServerDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _urlController = TextEditingController();
  final _commandController = TextEditingController();
  final _argsController = TextEditingController();
  
  MCPTransportType _transportType = MCPTransportType.http;
  bool _autoConnect = true;

  @override
  void initState() {
    super.initState();
    if (widget.server != null) {
      _populateFields(widget.server!);
    }
  }

  void _populateFields(MCPServer server) {
    _nameController.text = server.name;
    _descriptionController.text = server.description ?? '';
    _transportType = server.transport.type;
    _autoConnect = server.autoConnect;

    if (server.transport.type == MCPTransportType.http) {
      _urlController.text = server.transport.url ?? '';
    } else {
      _commandController.text = server.transport.command ?? '';
      _argsController.text = server.transport.args?.join(' ') ?? '';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _urlController.dispose();
    _commandController.dispose();
    _argsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 500,
        padding: const EdgeInsets.all(24.0),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Text(
                widget.server != null ? 'Edit MCP Server' : 'Add MCP Server',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 24),

              // Server name
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Server Name',
                  helperText: 'A friendly name for this server',
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a server name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Description
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description (Optional)',
                  helperText: 'What does this server provide?',
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 16),

              // Transport type
              Text(
                'Transport Type',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: RadioListTile<MCPTransportType>(
                      title: const Text('HTTP'),
                      subtitle: const Text('Web-based server'),
                      value: MCPTransportType.http,
                      groupValue: _transportType,
                      onChanged: (value) {
                        setState(() {
                          _transportType = value!;
                        });
                      },
                    ),
                  ),
                  Expanded(
                    child: RadioListTile<MCPTransportType>(
                      title: const Text('Process'),
                      subtitle: const Text('Local executable'),
                      value: MCPTransportType.stdio,
                      groupValue: _transportType,
                      onChanged: (value) {
                        setState(() {
                          _transportType = value!;
                        });
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Transport-specific fields
              if (_transportType == MCPTransportType.http) ...[
                TextFormField(
                  controller: _urlController,
                  decoration: const InputDecoration(
                    labelText: 'Server URL',
                    helperText: 'e.g., https://api.example.com/mcp',
                    prefixIcon: Icon(Icons.language),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter a server URL';
                    }
                    try {
                      Uri.parse(value.trim());
                    } catch (e) {
                      return 'Please enter a valid URL';
                    }
                    return null;
                  },
                ),
              ] else ...[
                TextFormField(
                  controller: _commandController,
                  decoration: const InputDecoration(
                    labelText: 'Command',
                    helperText: 'e.g., node, python, or path to executable',
                    prefixIcon: Icon(Icons.terminal),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter a command';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _argsController,
                  decoration: const InputDecoration(
                    labelText: 'Arguments (Optional)',
                    helperText: 'Space-separated arguments',
                    prefixIcon: Icon(Icons.list),
                  ),
                ),
              ],
              const SizedBox(height: 16),

              // Auto-connect option
              SwitchListTile(
                title: const Text('Auto-connect'),
                subtitle: const Text('Connect automatically when app starts'),
                value: _autoConnect,
                onChanged: (value) {
                  setState(() {
                    _autoConnect = value;
                  });
                },
              ),
              const SizedBox(height: 24),

              // Actions
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 16),
                  FilledButton(
                    onPressed: _saveServer,
                    child: Text(widget.server != null ? 'Update' : 'Add'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _saveServer() {
    if (!_formKey.currentState!.validate()) return;

    final transport = MCPTransport(
      type: _transportType,
      url: _transportType == MCPTransportType.http 
          ? _urlController.text.trim() 
          : null,
      command: _transportType == MCPTransportType.stdio 
          ? _commandController.text.trim() 
          : null,
      args: _transportType == MCPTransportType.stdio && _argsController.text.trim().isNotEmpty
          ? _argsController.text.trim().split(' ')
          : null,
    );

    final server = MCPServer(
      id: widget.server?.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
      name: _nameController.text.trim(),
      description: _descriptionController.text.trim().isEmpty 
          ? null 
          : _descriptionController.text.trim(),
      transport: transport,
      autoConnect: _autoConnect,
      createdAt: widget.server?.createdAt ?? DateTime.now(),
    );

    context.read<SettingsCubit>().saveMCPServer(server);
    Navigator.of(context).pop();
  }
}
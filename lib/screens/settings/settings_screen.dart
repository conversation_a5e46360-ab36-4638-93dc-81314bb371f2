import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../blocs/settings/settings_cubit.dart';
import 'ai_providers_tab.dart';
import 'mcp_servers_tab.dart';
import 'general_tab.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(
              icon: Icon(Icons.tune),
              text: 'General',
            ),
            Tab(
              icon: Icon(Icons.psychology),
              text: 'AI Providers',
            ),
            Tab(
              icon: Icon(Icons.extension),
              text: 'MCP Servers',
            ),
          ],
        ),
      ),
      body: BlocProvider(
        create: (context) => SettingsCubit()..loadSettings(),
        child: TabBarView(
          controller: _tabController,
          children: const [
            GeneralTab(),
            AIProvidersTab(),
            MCPServersTab(),
          ],
        ),
      ),
    );
  }
}
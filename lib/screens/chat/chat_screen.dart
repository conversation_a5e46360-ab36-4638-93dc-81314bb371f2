import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gpt_markdown/gpt_markdown.dart';
import '../../widgets/empty_state.dart';
import '../../widgets/persona_selector.dart';
import '../../blocs/settings/settings_cubit.dart';
import '../../blocs/personas/personas_cubit.dart';
import '../../blocs/personas/personas_state.dart';
import '../../models/models.dart';
import '../../services/ai_service.dart';

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final List<Message> _messages = [];
  final AIService _aiService = AIService();
  bool _isTyping = false;
  AIProvider? _selectedProvider;
  String? _selectedModel;
  AIPersona? _selectedPersona;

  @override
  void initState() {
    super.initState();
    // Load settings when the chat screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<SettingsCubit>().loadSettings();
      context.read<PersonasCubit>().loadPersonas();
    });
  }

  @override
  void dispose() {
    _messageController.dispose();
    _aiService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: BlocBuilder<SettingsCubit, SettingsState>(
          builder: (context, state) {
            if (state is SettingsLoaded) {
              final providers = state.aiProviders.values.toList();

              // If no provider is selected or the selected provider is no longer available
              if (_selectedProvider == null ||
                  !providers.any((p) => p.id == _selectedProvider!.id)) {
                _selectedProvider = providers.isNotEmpty
                    ? providers.first
                    : null;
                _selectedModel = _selectedProvider?.defaultModel;
              } else {
                // Update the selected provider with the latest data
                final updatedProvider = providers.firstWhere(
                  (p) => p.id == _selectedProvider!.id,
                  orElse: () => providers.isNotEmpty
                      ? providers.first
                      : _selectedProvider!,
                );
                _selectedProvider = updatedProvider;

                // Update selected model if it's no longer valid
                if (_selectedModel != null &&
                    updatedProvider.models.isNotEmpty &&
                    !updatedProvider.models.contains(_selectedModel!)) {
                  _selectedModel = updatedProvider.defaultModel;
                }
              }
            }

            return Row(
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundColor: Theme.of(
                    context,
                  ).colorScheme.primaryContainer,
                  child: Text(
                    _getProviderIcon(_selectedProvider?.type ?? 'default'),
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _selectedProvider?.name ?? 'No Provider',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        _selectedModel ?? 'No model selected',
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.normal,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
        actions: [
          // Persona selector
          BlocBuilder<PersonasCubit, PersonasState>(
            builder: (context, state) {
              if (state is PersonasLoaded) {
                // Always ensure we have a selected persona, even if the state doesn't have one
                if (_selectedPersona == null) {
                  _selectedPersona =
                      state.selectedPersona ??
                      (state.personas.isNotEmpty ? state.personas.first : null);
                }
                return PersonaSelector(
                  selectedPersona: _selectedPersona,
                  onPersonaSelected: (persona) {
                    setState(() {
                      _selectedPersona = persona;
                    });
                    context.read<PersonasCubit>().selectPersona(persona);
                  },
                  compact: true,
                  showLabel: false,
                );
              }
              return const SizedBox.shrink();
            },
          ),
          const SizedBox(width: 8),
          IconButton(
            icon: const Icon(Icons.tune),
            tooltip: 'Change AI Provider',
            onPressed: () => _showProviderModelSelector(context),
          ),
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {
              // TODO: Show chat options menu
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Messages area
          Expanded(
            child: _messages.isEmpty
                ? _buildEmptyState()
                : _buildMessagesList(),
          ),

          // Input area
          _buildInputArea(),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    if (_selectedProvider == null) {
      return EmptyState(
        icon: Icons.psychology_outlined,
        title: 'No AI Provider Selected',
        subtitle: 'Configure an AI provider in settings to start chatting.',
        actionText: 'Go to Settings',
        onAction: () => Navigator.pushNamed(context, '/settings'),
      );
    }

    return const EmptyState(
      icon: Icons.chat_bubble_outline,
      title: 'Start a conversation',
      subtitle:
          'Ask me anything! I\'m here to help with your questions and tasks.',
    );
  }

  Widget _buildMessagesList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _messages.length + (_isTyping ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == _messages.length && _isTyping) {
          return _buildTypingIndicator();
        }
        return _buildMessageBubble(_messages[index]);
      },
    );
  }

  Widget _buildMessageBubble(Message message) {
    final isUser = message.isUser;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Column(
        crossAxisAlignment: isUser
            ? CrossAxisAlignment.end
            : CrossAxisAlignment.start,
        children: [
          // Message label
          Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Text(
              isUser ? 'You' : 'AI',
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          // Message content
          Container(
            width: double.infinity,
            alignment: isUser ? Alignment.centerRight : Alignment.centerLeft,
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.9,
              ),
              child: isUser
                  ? SelectableText(
                      message.content,
                      style: Theme.of(context).textTheme.bodyMedium,
                    )
                  : GptMarkdown(
                      message.content,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTypingIndicator() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Message label
          Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Text(
              'AI',
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          // Typing animation
          const _TypingAnimation(),
        ],
      ),
    );
  }

  Widget _buildInputArea() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Persona indicator
            if (_selectedPersona != null)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                child: Row(
                  children: [
                    Text(
                      _selectedPersona!.avatar,
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Chatting as ${_selectedPersona!.name}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            // Input row
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Theme.of(
                          context,
                        ).colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(28),
                        border: Border.all(
                          color: Theme.of(
                            context,
                          ).colorScheme.outline.withValues(alpha: 0.1),
                        ),
                      ),
                      child: TextField(
                        controller: _messageController,
                        decoration: InputDecoration(
                          hintText: 'Type a message...',
                          hintStyle: TextStyle(
                            color: Theme.of(context)
                                .colorScheme
                                .onSurfaceVariant
                                .withValues(alpha: 0.6),
                          ),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 14,
                          ),
                          prefixIcon: Icon(
                            Icons.chat_bubble_outline,
                            color: Theme.of(context)
                                .colorScheme
                                .onSurfaceVariant
                                .withValues(alpha: 0.5),
                            size: 20,
                          ),
                        ),
                        maxLines: null,
                        textCapitalization: TextCapitalization.sentences,
                        onSubmitted: (_) => _sendMessage(),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  BlocBuilder<SettingsCubit, SettingsState>(
                    builder: (context, state) {
                      final hasProvider =
                          state is SettingsLoaded &&
                          state.aiProviders.isNotEmpty &&
                          _selectedProvider != null;

                      return FloatingActionButton.small(
                        onPressed:
                            (_messageController.text.trim().isEmpty ||
                                !hasProvider ||
                                _isTyping)
                            ? null
                            : _sendMessage,
                        heroTag: 'send_message',
                        child: _isTyping
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                            : const Icon(Icons.send, size: 20),
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _sendMessage() async {
    final text = _messageController.text.trim();
    if (text.isEmpty || _selectedProvider == null) return;

    // Add user message
    setState(() {
      _messages.add(
        Message(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          content: text,
          isUser: true,
          timestamp: DateTime.now(),
        ),
      );
      _messageController.clear();
      _isTyping = true;
    });

    HapticFeedback.lightImpact();

    try {
      // Check if provider supports streaming
      final supportsStreaming =
          _selectedProvider!.type.toLowerCase() == 'openrouter' ||
          _selectedProvider!.type.toLowerCase() == 'openai';

      if (supportsStreaming) {
        // Use streaming response
        await _handleStreamingResponse(text);
      } else {
        // Use non-streaming response
        await _handleNonStreamingResponse(text);
      }
    } catch (e) {
      // Only add error message if no message was already added
      if (mounted) {
        setState(() {
          _isTyping = false;
        });
      }
    }
  }

  Future<void> _handleStreamingResponse(String userMessage) async {
    try {
      // Add empty assistant message that will be updated with streaming content
      setState(() {
        _messages.add(
          Message(
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            content: '',
            isUser: false,
            timestamp: DateTime.now(),
          ),
        );
      });

      final messageIndex = _messages.length - 1;
      final streamingContent = StringBuffer();

      // Convert Message to ChatMessage for AI service with persona context
      final chatMessages = _prepareChatMessages();

      await for (final chunk in _aiService.sendMessageStream(
        provider: _selectedProvider!,
        messages: chatMessages,
        selectedModel: _selectedModel,
        temperature: 0.7,
        maxTokens: 1000,
      )) {
        if (mounted) {
          streamingContent.write(chunk);
          setState(() {
            _messages[messageIndex] = Message(
              id: _messages[messageIndex].id,
              content: streamingContent.toString(),
              isUser: false,
              timestamp: _messages[messageIndex].timestamp,
            );
          });
        }
      }

      if (mounted) {
        setState(() {
          _isTyping = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          // Update the last message with error
          if (_messages.isNotEmpty && !_messages.last.isUser) {
            final lastMessage = _messages.last;
            _messages[_messages.length - 1] = Message(
              id: lastMessage.id,
              content: 'Error: ${e.toString()}',
              isUser: false,
              timestamp: lastMessage.timestamp,
            );
          }
          _isTyping = false;
        });
      }
    }
  }

  Future<void> _handleNonStreamingResponse(String userMessage) async {
    try {
      // Convert Message to ChatMessage for AI service with persona context
      final chatMessages = _prepareChatMessages();

      final response = await _aiService.sendMessage(
        provider: _selectedProvider!,
        messages: chatMessages,
        selectedModel: _selectedModel,
        temperature: 0.7,
        maxTokens: 1000,
      );

      if (mounted) {
        setState(() {
          _messages.add(
            Message(
              id: DateTime.now().millisecondsSinceEpoch.toString(),
              content: response,
              isUser: false,
              timestamp: DateTime.now(),
            ),
          );
          _isTyping = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _messages.add(
            Message(
              id: DateTime.now().millisecondsSinceEpoch.toString(),
              content: 'Error: ${e.toString()}',
              isUser: false,
              timestamp: DateTime.now(),
            ),
          );
          _isTyping = false;
        });
      }
    }
  }

  String _getProviderIcon(String type) {
    switch (type.toLowerCase()) {
      case 'openrouter':
        return '🌐';
      case 'openai':
        return '🤖';
      case 'anthropic':
        return '🧠';
      case 'ollama':
        return '🦙';
      default:
        return '💭';
    }
  }

  void _showProviderModelSelector(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (modalContext) => BlocBuilder<SettingsCubit, SettingsState>(
        builder: (context, state) {
          if (state is! SettingsLoaded) {
            return Container(
              height: 200,
              padding: const EdgeInsets.all(16),
              child: const Center(child: CircularProgressIndicator()),
            );
          }

          final providers = state.aiProviders.values.toList();

          if (providers.isEmpty) {
            return Container(
              height: 300,
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  const Icon(Icons.psychology_outlined, size: 48),
                  const SizedBox(height: 16),
                  Text(
                    'No AI Providers',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Configure AI providers in settings to start chatting',
                  ),
                  const SizedBox(height: 24),
                  FilledButton(
                    onPressed: () {
                      Navigator.pop(modalContext);
                      Navigator.pushNamed(context, '/settings');
                    },
                    child: const Text('Go to Settings'),
                  ),
                ],
              ),
            );
          }

          return _ProviderModelSelector(
            providers: providers,
            selectedProvider: _selectedProvider,
            selectedModel: _selectedModel,
            onSelectionChanged: (provider, model) {
              setState(() {
                _selectedProvider = provider;
                _selectedModel = model;
              });
              Navigator.pop(modalContext);
            },
            onManageProviders: () {
              Navigator.pop(modalContext);
              Navigator.pushNamed(context, '/settings');
            },
            getProviderIcon: _getProviderIcon,
          );
        },
      ),
    );
  }

  /// Prepares chat messages with persona system prompt
  List<ChatMessage> _prepareChatMessages() {
    final messages = <ChatMessage>[];

    // Add persona system prompt as the first message if a persona is selected
    if (_selectedPersona != null && _selectedPersona!.systemPrompt.isNotEmpty) {
      messages.add(
        ChatMessage(
          content: "System: ${_selectedPersona!.systemPrompt}",
          isUser: false,
        ),
      );
    }

    // Add conversation messages
    messages.addAll(
      _messages
          .where((msg) => msg.content.isNotEmpty)
          .map((msg) => ChatMessage(content: msg.content, isUser: msg.isUser)),
    );

    return messages;
  }
}

class _ProviderModelSelector extends StatelessWidget {
  final List<AIProvider> providers;
  final AIProvider? selectedProvider;
  final String? selectedModel;
  final Function(AIProvider, String?) onSelectionChanged;
  final VoidCallback onManageProviders;
  final String Function(String) getProviderIcon;

  const _ProviderModelSelector({
    required this.providers,
    required this.selectedProvider,
    required this.selectedModel,
    required this.onSelectionChanged,
    required this.onManageProviders,
    required this.getProviderIcon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.7,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(top: 12, bottom: 8),
            decoration: BoxDecoration(
              color: Theme.of(
                context,
              ).colorScheme.onSurfaceVariant.withValues(alpha: 0.4),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            child: Row(
              children: [
                const Icon(Icons.tune),
                const SizedBox(width: 12),
                Text(
                  'Select AI Provider & Model',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          // Provider list
          Flexible(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: providers.length,
              itemBuilder: (context, index) {
                final provider = providers[index];
                final isSelected = selectedProvider?.id == provider.id;

                return ExpansionTile(
                  leading: CircleAvatar(
                    radius: 20,
                    backgroundColor: isSelected
                        ? Theme.of(context).colorScheme.primaryContainer
                        : Theme.of(context).colorScheme.surfaceContainerHighest,
                    child: Text(
                      getProviderIcon(provider.type),
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                  title: Text(
                    provider.name,
                    style: TextStyle(
                      fontWeight: isSelected
                          ? FontWeight.bold
                          : FontWeight.normal,
                    ),
                  ),
                  subtitle: Text(
                    provider.type.toUpperCase(),
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.primary,
                      fontSize: 12,
                    ),
                  ),
                  trailing: isSelected
                      ? Icon(
                          Icons.check_circle,
                          color: Theme.of(context).colorScheme.primary,
                        )
                      : null,
                  children: [
                    if (provider.models.isNotEmpty) ...[
                      const Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16),
                        child: Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            'Available Models:',
                            style: TextStyle(fontWeight: FontWeight.w600),
                          ),
                        ),
                      ),
                      ...provider.models.map(
                        (model) => ListTile(
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 32,
                          ),
                          title: Text(
                            model,
                            style: const TextStyle(fontSize: 14),
                          ),
                          trailing: selectedModel == model
                              ? Icon(
                                  Icons.radio_button_checked,
                                  color: Theme.of(context).colorScheme.primary,
                                )
                              : const Icon(Icons.radio_button_unchecked),
                          onTap: () => onSelectionChanged(provider, model),
                        ),
                      ),
                    ] else if (provider.defaultModel != null) ...[
                      ListTile(
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 32,
                        ),
                        title: Text(
                          provider.defaultModel!,
                          style: const TextStyle(fontSize: 14),
                        ),
                        subtitle: const Text('Default Model'),
                        trailing: Icon(
                          Icons.radio_button_checked,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        onTap: () =>
                            onSelectionChanged(provider, provider.defaultModel),
                      ),
                    ] else
                      const ListTile(
                        contentPadding: EdgeInsets.symmetric(horizontal: 32),
                        title: Text(
                          'No models configured',
                          style: TextStyle(fontSize: 14),
                        ),
                        subtitle: Text(
                          'Update provider settings to add models',
                        ),
                      ),
                  ],
                );
              },
            ),
          ),
          // Footer
          Padding(
            padding: const EdgeInsets.all(24),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: onManageProviders,
                    child: const Text('Manage Providers'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _TypingAnimation extends StatefulWidget {
  const _TypingAnimation();

  @override
  State<_TypingAnimation> createState() => _TypingAnimationState();
}

class _TypingAnimationState extends State<_TypingAnimation>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    )..repeat();

    _animations = List.generate(3, (index) {
      return Tween<double>(begin: 0.4, end: 1.0).animate(
        CurvedAnimation(
          parent: _controller,
          curve: Interval(
            index * 0.2,
            (index * 0.2) + 0.4,
            curve: Curves.easeInOut,
          ),
        ),
      );
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(3, (index) {
        return AnimatedBuilder(
          animation: _animations[index],
          builder: (context, child) {
            return Container(
              margin: EdgeInsets.only(right: index < 2 ? 4 : 0),
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Theme.of(context).colorScheme.onSurface.withValues(
                  alpha: _animations[index].value,
                ),
              ),
            );
          },
        );
      }),
    );
  }
}

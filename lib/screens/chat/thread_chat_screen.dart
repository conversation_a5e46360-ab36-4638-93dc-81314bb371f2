import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gpt_markdown/gpt_markdown.dart';
import '../../blocs/threads/threads_cubit.dart';
import '../../blocs/threads/threads_state.dart';
import '../../blocs/personas/personas_cubit.dart';
import '../../blocs/personas/personas_state.dart';
import '../../blocs/settings/settings_cubit.dart';
import '../../models/models.dart';
import '../../services/ai_service.dart';
import '../../services/mcp_tool_execution_service.dart';
import '../../widgets/widgets.dart';
import '../spaces/create_thread_dialog.dart';

class ThreadChatScreen extends StatefulWidget {
  final Thread thread;
  final Space space;

  const ThreadChatScreen({
    super.key,
    required this.thread,
    required this.space,
  });

  @override
  State<ThreadChatScreen> createState() => _ThreadChatScreenState();
}

class _ThreadChatScreenState extends State<ThreadChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final AIService _aiService = AIService();
  final MCPToolExecutionService _toolExecutionService =
      MCPToolExecutionService();
  bool _isTyping = false;
  AIProvider? _selectedProvider;
  String? _selectedModel;
  AIPersona? _selectedPersona;
  late Thread _currentThread;

  @override
  void initState() {
    super.initState();
    _currentThread = widget.thread;
    _selectedModel = widget.thread.modelId;

    // Load the thread's persona if specified
    if (widget.thread.personaId != null) {
      context.read<PersonasCubit>().loadPersonas();
    }

    // Load AI providers from settings
    _loadAIProviders();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _aiService.dispose();
    super.dispose();
  }

  void _loadAIProviders() {
    final settingsState = context.read<SettingsCubit>().state;
    if (settingsState is SettingsLoaded) {
      final providers = settingsState.aiProviders.values.toList();
      if (providers.isNotEmpty) {
        _selectedProvider = providers.first;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _currentThread.title,
              style: const TextStyle(fontSize: 16),
              overflow: TextOverflow.ellipsis,
            ),
            Text(
              '${widget.space.icon} ${widget.space.title}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
        actions: [
          // Thread switcher
          IconButton(
            onPressed: _showThreadSwitcher,
            icon: const Icon(Icons.list),
            tooltip: 'Switch Thread',
          ),

          // Persona indicator
          BlocBuilder<PersonasCubit, PersonasState>(
            builder: (context, state) {
              if (state is PersonasLoaded && widget.thread.personaId != null) {
                final persona = state.personas.firstWhere(
                  (p) => p.id == widget.thread.personaId,
                  orElse: () => state.personas.first,
                );
                _selectedPersona = persona;
                return Container(
                  margin: const EdgeInsets.only(right: 8),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        persona.avatar,
                        style: const TextStyle(fontSize: 14),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        persona.name,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(
                            context,
                          ).colorScheme.onPrimaryContainer,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                );
              }
              return const SizedBox.shrink();
            },
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'edit':
                  _editThread();
                  break;
                case 'delete':
                  _deleteThread();
                  break;
                case 'settings':
                  _showProviderModelSelector();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit),
                    SizedBox(width: 8),
                    Text('Edit Thread'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.tune),
                    SizedBox(width: 8),
                    Text('AI Settings'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete),
                    SizedBox(width: 8),
                    Text('Delete Thread'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Messages area
          Expanded(
            child: _currentThread.messages.isEmpty
                ? _buildEmptyState()
                : _buildMessagesList(),
          ),

          // Input area
          _buildInputArea(),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    if (_selectedProvider == null) {
      return EmptyState(
        icon: Icons.psychology_outlined,
        title: 'No AI Provider Selected',
        subtitle: 'Configure an AI provider in settings to start chatting.',
        actionText: 'Go to Settings',
        onAction: () => Navigator.pushNamed(context, '/settings'),
      );
    }

    return EmptyState(
      icon: Icons.chat_bubble_outline,
      title: 'Start the conversation',
      subtitle: _selectedPersona != null
          ? 'Chat with ${_selectedPersona!.name} about anything!'
          : 'Ask me anything! I\'m here to help with your questions and tasks.',
    );
  }

  Widget _buildMessagesList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _currentThread.messages.length + (_isTyping ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == _currentThread.messages.length && _isTyping) {
          return _buildTypingIndicator();
        }
        return _buildMessageBubble(_currentThread.messages[index]);
      },
    );
  }

  Widget _buildMessageBubble(Message message) {
    final isUser = message.isUser;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Column(
        crossAxisAlignment: isUser
            ? CrossAxisAlignment.end
            : CrossAxisAlignment.start,
        children: [
          // Message label
          Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Text(
              isUser ? 'You' : 'AI',
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          // Message content
          Container(
            width: double.infinity,
            alignment: isUser ? Alignment.centerRight : Alignment.centerLeft,
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.9,
              ),
              child: Column(
                crossAxisAlignment: isUser
                    ? CrossAxisAlignment.end
                    : CrossAxisAlignment.start,
                children: [
                  isUser
                      ? SelectableText(
                          message.content,
                          style: Theme.of(context).textTheme.bodyMedium,
                        )
                      : GptMarkdown(
                          message.content,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                  if (!isUser &&
                      message.toolResults != null &&
                      message.toolResults!.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    ToolResultDisplay(toolExecutions: message.toolResults!),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTypingIndicator() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          CircleAvatar(
            radius: 16,
            child: Text(
              _selectedPersona?.avatar ?? '🤖',
              style: const TextStyle(fontSize: 14),
            ),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(
                18,
              ).copyWith(bottomLeft: const Radius.circular(4)),
            ),
            child: const _TypingAnimation(),
          ),
        ],
      ),
    );
  }

  Widget _buildInputArea() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: TextField(
                controller: _messageController,
                decoration: InputDecoration(
                  hintText: 'Type a message...',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(24),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Theme.of(
                    context,
                  ).colorScheme.surfaceContainerHighest,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12,
                  ),
                ),
                maxLines: null,
                textCapitalization: TextCapitalization.sentences,
                onSubmitted: (_) => _sendMessage(),
              ),
            ),
            const SizedBox(width: 8),
            FloatingActionButton.small(
              onPressed:
                  (_messageController.text.trim().isEmpty ||
                      _selectedProvider == null ||
                      _isTyping)
                  ? null
                  : _sendMessage,
              heroTag: 'send_message',
              child: _isTyping
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.send, size: 20),
            ),
          ],
        ),
      ),
    );
  }

  void _sendMessage() async {
    final text = _messageController.text.trim();
    if (text.isEmpty || _selectedProvider == null) return;

    // Create user message
    final userMessage = Message(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: text,
      isUser: true,
      timestamp: DateTime.now(),
    );

    // Add message to thread and update UI
    setState(() {
      _currentThread = _currentThread.copyWith(
        messages: [..._currentThread.messages, userMessage],
      );
      _messageController.clear();
      _isTyping = true;
    });

    // Save message to repository
    context.read<ThreadsCubit>().addMessageToThread(
      _currentThread.id,
      userMessage,
    );

    HapticFeedback.lightImpact();

    try {
      // Prepare chat messages with persona context
      final chatMessages = _prepareChatMessages();

      // Get AI response
      final response = await _aiService.sendMessage(
        provider: _selectedProvider!,
        messages: chatMessages,
        selectedModel: _selectedModel,
        temperature: 0.7,
        maxTokens: 1000,
      );

      // Check for tool calls in the response and execute them
      final toolCalls = _toolExecutionService.parseToolCallsFromResponse(
        response,
      );

      List<ToolExecution> toolResults = [];
      if (toolCalls.isNotEmpty) {
        // Execute tools
        toolResults = await _toolExecutionService.executeTools(toolCalls);
      }

      // Create AI message with tool results
      final aiMessage = Message(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        content: response,
        isUser: false,
        timestamp: DateTime.now(),
        toolResults: toolResults.isNotEmpty ? toolResults : null,
      );

      if (mounted) {
        setState(() {
          _currentThread = _currentThread.copyWith(
            messages: [..._currentThread.messages, aiMessage],
          );
          _isTyping = false;
        });

        // Save AI message to repository
        context.read<ThreadsCubit>().addMessageToThread(
          _currentThread.id,
          aiMessage,
        );
      }
    } catch (e) {
      if (mounted) {
        final errorMessage = Message(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          content: 'Error: ${e.toString()}',
          isUser: false,
          timestamp: DateTime.now(),
        );

        setState(() {
          _currentThread = _currentThread.copyWith(
            messages: [..._currentThread.messages, errorMessage],
          );
          _isTyping = false;
        });

        context.read<ThreadsCubit>().addMessageToThread(
          _currentThread.id,
          errorMessage,
        );
      }
    }
  }

  List<ChatMessage> _prepareChatMessages() {
    final messages = <ChatMessage>[];

    // Add persona system prompt if available
    if (_selectedPersona != null && _selectedPersona!.systemPrompt.isNotEmpty) {
      messages.add(
        ChatMessage(
          content: "System: ${_selectedPersona!.systemPrompt}",
          isUser: false,
        ),
      );
    }

    // Add space system prompt if available
    if (widget.space.systemPrompt != null &&
        widget.space.systemPrompt!.isNotEmpty) {
      messages.add(
        ChatMessage(
          content: "Space Context: ${widget.space.systemPrompt}",
          isUser: false,
        ),
      );
    }

    // Add conversation messages
    messages.addAll(
      _currentThread.messages
          .where((msg) => msg.content.isNotEmpty)
          .map((msg) => ChatMessage(content: msg.content, isUser: msg.isUser)),
    );

    return messages;
  }

  void _editThread() {
    showDialog(
      context: context,
      builder: (context) =>
          CreateThreadDialog(space: widget.space, thread: _currentThread),
    ).then((result) {
      // Refresh thread data if it was updated
      if (result == true && mounted) {
        // The thread was updated, we need to get the latest version
        // Since we don't have a direct way to get updated thread,
        // we'll listen to the ThreadsCubit stream
        final threadsCubit = context.read<ThreadsCubit>();
        final threadsState = threadsCubit.state;
        if (threadsState is ThreadsLoaded) {
          final updatedThread = threadsState.threads.firstWhere(
            (t) => t.id == _currentThread.id,
            orElse: () => _currentThread,
          );
          setState(() {
            _currentThread = updatedThread;
            _selectedModel = updatedThread.modelId;
          });
        }
      }
    });
  }

  void _deleteThread() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Thread'),
        content: Text(
          'Are you sure you want to delete "${_currentThread.title}"?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Go back to space detail
              context.read<ThreadsCubit>().deleteThread(_currentThread.id);
            },
            style: FilledButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showProviderModelSelector() {
    // TODO: Show provider/model selector
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('AI settings functionality coming soon')),
    );
  }

  void _showThreadSwitcher() {
    showModalBottomSheet(
      context: context,
      builder: (context) => _ThreadSwitcherBottomSheet(
        currentSpace: widget.space,
        currentThread: _currentThread,
        onThreadSelected: (thread) {
          Navigator.pop(context); // Close bottom sheet
          _switchToThread(thread);
        },
      ),
    );
  }

  void _switchToThread(Thread thread) {
    if (thread.id == _currentThread.id) return; // Already on this thread

    setState(() {
      _currentThread = thread;
      _selectedModel = thread.modelId;
      _isTyping = false;
    });

    // Load the thread's persona if specified
    if (thread.personaId != null) {
      context.read<PersonasCubit>().loadPersonas();
    }
  }
}

class _TypingAnimation extends StatefulWidget {
  const _TypingAnimation();

  @override
  State<_TypingAnimation> createState() => _TypingAnimationState();
}

class _TypingAnimationState extends State<_TypingAnimation>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(
      3,
      (index) => AnimationController(
        duration: const Duration(milliseconds: 600),
        vsync: this,
      ),
    );

    _animations = _controllers.map((controller) {
      return Tween<double>(
        begin: 0.4,
        end: 1.0,
      ).animate(CurvedAnimation(parent: controller, curve: Curves.easeInOut));
    }).toList();

    _startAnimations();
  }

  void _startAnimations() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 200), () {
        if (mounted) {
          _controllers[i].repeat(reverse: true);
        }
      });
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(3, (index) {
        return AnimatedBuilder(
          animation: _animations[index],
          builder: (context, child) {
            return Container(
              margin: EdgeInsets.only(right: index < 2 ? 4 : 0),
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Theme.of(context).colorScheme.onSurface.withValues(
                  alpha: _animations[index].value,
                ),
              ),
            );
          },
        );
      }),
    );
  }
}

class _ThreadSwitcherBottomSheet extends StatelessWidget {
  final Space currentSpace;
  final Thread currentThread;
  final Function(Thread) onThreadSelected;

  const _ThreadSwitcherBottomSheet({
    required this.currentSpace,
    required this.currentThread,
    required this.onThreadSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Text(
                currentSpace.icon ?? '📁',
                style: const TextStyle(fontSize: 20),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Threads in ${currentSpace.title}',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
              ),
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.close),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Thread list
          Flexible(
            child: BlocBuilder<ThreadsCubit, ThreadsState>(
              builder: (context, state) {
                if (state is ThreadsLoaded) {
                  final threads = state.threads;

                  if (threads.isEmpty) {
                    return const Center(
                      child: Text('No threads in this space'),
                    );
                  }

                  return ListView.builder(
                    shrinkWrap: true,
                    itemCount: threads.length,
                    itemBuilder: (context, index) {
                      final thread = threads[index];
                      final isCurrentThread = thread.id == currentThread.id;

                      return ListTile(
                        leading: CircleAvatar(
                          backgroundColor: isCurrentThread
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(
                                  context,
                                ).colorScheme.surfaceContainerHighest,
                          child: Text(
                            thread.title.isNotEmpty
                                ? thread.title[0].toUpperCase()
                                : 'T',
                            style: TextStyle(
                              color: isCurrentThread
                                  ? Theme.of(context).colorScheme.onPrimary
                                  : Theme.of(context).colorScheme.onSurface,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        title: Text(
                          thread.title,
                          style: TextStyle(
                            fontWeight: isCurrentThread
                                ? FontWeight.bold
                                : FontWeight.normal,
                          ),
                        ),
                        subtitle: Text(
                          '${thread.messages.length} messages • ${_formatDate(thread.created)}',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                        trailing: isCurrentThread
                            ? Icon(
                                Icons.check_circle,
                                color: Theme.of(context).colorScheme.primary,
                              )
                            : null,
                        onTap: isCurrentThread
                            ? null
                            : () => onThreadSelected(thread),
                      );
                    },
                  );
                }

                if (state is ThreadsLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                return const Center(child: Text('Failed to load threads'));
              },
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

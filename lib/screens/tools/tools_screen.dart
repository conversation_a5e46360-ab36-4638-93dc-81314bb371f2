import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../widgets/widgets.dart';
import '../../blocs/mcp_tools/mcp_tools_cubit.dart';
import '../../models/models.dart';

class ToolsScreen extends StatefulWidget {
  const ToolsScreen({super.key});

  @override
  State<ToolsScreen> createState() => _ToolsScreenState();
}

class _ToolsScreenState extends State<ToolsScreen> {
  @override
  void initState() {
    super.initState();
    // Initialize MCP tools when screen loads
    context.read<MCPToolsCubit>().initialize();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tools'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<MCPToolsCubit>().refreshTools();
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              // Navigate to MCP settings
              Navigator.pushNamed(context, '/settings');
            },
          ),
        ],
      ),
      body: BlocBuilder<MCPToolsCubit, MCPToolsState>(
        builder: (context, state) {
          if (state is MCPToolsLoading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is MCPToolsError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error Loading Tools',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.message,
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      context.read<MCPToolsCubit>().refreshTools();
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          } else if (state is MCPToolsLoaded) {
            if (state.allTools.isEmpty) {
              return EmptyState(
                icon: Icons.extension,
                message: 'No Tools Available',
                subtitle:
                    'Connect MCP servers in settings to access powerful tools and resources',
                actionText: 'Configure MCP Servers',
                onAction: () => Navigator.pushNamed(context, '/settings'),
              );
            }

            return _buildToolsList(state);
          }

          return EmptyState(
            icon: Icons.extension,
            message: 'No Tools Connected',
            subtitle:
                'Connect MCP servers in settings to access powerful tools and resources',
            actionText: 'Configure MCP Servers',
            onAction: () => Navigator.pushNamed(context, '/settings'),
          );
        },
      ),
    );
  }

  Widget _buildToolsList(MCPToolsLoaded state) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: state.toolsByServer.length,
      itemBuilder: (context, index) {
        final serverId = state.toolsByServer.keys.elementAt(index);
        final tools = state.toolsByServer[serverId]!;
        final isConnected = state.isServerConnected(serverId);

        return _buildServerSection(serverId, tools, isConnected);
      },
    );
  }

  Widget _buildServerSection(
    String serverId,
    List<McpTool> tools,
    bool isConnected,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ListTile(
            leading: Icon(
              isConnected ? Icons.cloud_done : Icons.cloud_off,
              color: isConnected ? Colors.green : Colors.red,
            ),
            title: Text(
              serverId,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Text(
              isConnected ? '${tools.length} tools available' : 'Disconnected',
            ),
            trailing: isConnected
                ? const Icon(Icons.check_circle, color: Colors.green)
                : const Icon(Icons.error, color: Colors.red),
          ),
          if (tools.isNotEmpty) ...[
            const Divider(height: 1),
            ...tools.map((tool) => _buildToolTile(tool)),
          ],
        ],
      ),
    );
  }

  Widget _buildToolTile(McpTool tool) {
    return ListTile(
      leading: const Icon(Icons.extension),
      title: Text(tool.name),
      subtitle: Text(
        tool.description.isNotEmpty
            ? tool.description
            : 'No description available',
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
      trailing: const Icon(Icons.chevron_right),
      onTap: () {
        _showToolDetails(tool);
      },
    );
  }

  void _showToolDetails(McpTool tool) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(tool.name),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (tool.description.isNotEmpty) ...[
                const Text(
                  'Description:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                Text(tool.description),
                const SizedBox(height: 16),
              ],
              const Text(
                'Server:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text(tool.serverId),
              if (tool.parameters.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Text(
                  'Parameters:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Theme.of(
                      context,
                    ).colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    tool.parameters.toString(),
                    style: const TextStyle(fontFamily: 'monospace'),
                  ),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

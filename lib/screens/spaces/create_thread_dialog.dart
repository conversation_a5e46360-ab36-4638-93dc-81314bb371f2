import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../blocs/threads/threads_cubit.dart';
import '../../blocs/threads/threads_state.dart';
import '../../blocs/personas/personas_cubit.dart';
import '../../blocs/personas/personas_state.dart';
import '../../models/models.dart';

class CreateThreadDialog extends StatefulWidget {
  final Space space;
  final Thread? thread; // For editing existing thread

  const CreateThreadDialog({super.key, required this.space, this.thread});

  @override
  State<CreateThreadDialog> createState() => _CreateThreadDialogState();
}

class _CreateThreadDialogState extends State<CreateThreadDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  String _selectedModelId = 'gpt-4o-mini'; // Default model
  String? _selectedPersonaId;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    // Initialize form with existing thread data if editing
    if (widget.thread != null) {
      _titleController.text = widget.thread!.title;
      _selectedModelId = widget.thread!.modelId;
      _selectedPersonaId = widget.thread!.personaId;
    } else {
      // Use space's default persona if available
      _selectedPersonaId = widget.space.defaultPersonaId;
    }

    // Load personas for selection
    context.read<PersonasCubit>().loadPersonas();
  }

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ThreadsCubit, ThreadsState>(
      listener: (context, state) {
        if (state is ThreadCreated || state is ThreadsLoaded) {
          Navigator.pop(context);
        } else if (state is ThreadsError) {
          setState(() => _isLoading = false);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error: ${state.message}'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      },
      child: AlertDialog(
        title: Text(widget.thread != null ? 'Edit Thread' : 'Create Thread'),
        content: SizedBox(
          width: MediaQuery.of(context).size.width * 0.8,
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Thread title
                TextFormField(
                  controller: _titleController,
                  decoration: const InputDecoration(
                    labelText: 'Thread Title',
                    hintText: 'Enter a descriptive title for this conversation',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter a thread title';
                    }
                    return null;
                  },
                  autofocus: true,
                ),

                const SizedBox(height: 16),

                // AI Model selection
                DropdownButtonFormField<String>(
                  value: _selectedModelId,
                  decoration: const InputDecoration(
                    labelText: 'AI Model',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(
                      value: 'gpt-4o-mini',
                      child: Text('GPT-4o Mini'),
                    ),
                    DropdownMenuItem(value: 'gpt-4o', child: Text('GPT-4o')),
                    DropdownMenuItem(
                      value: 'claude-3-5-sonnet-20241022',
                      child: Text('Claude 3.5 Sonnet'),
                    ),
                    DropdownMenuItem(
                      value: 'claude-3-5-haiku-20241022',
                      child: Text('Claude 3.5 Haiku'),
                    ),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      setState(() => _selectedModelId = value);
                    }
                  },
                ),

                const SizedBox(height: 16),

                // Persona selection
                BlocBuilder<PersonasCubit, PersonasState>(
                  builder: (context, state) {
                    if (state is PersonasLoaded) {
                      return DropdownButtonFormField<String?>(
                        value: _selectedPersonaId,
                        decoration: const InputDecoration(
                          labelText: 'AI Persona (Optional)',
                          border: OutlineInputBorder(),
                        ),
                        items: [
                          const DropdownMenuItem<String?>(
                            value: null,
                            child: Text('None'),
                          ),
                          ...state.personas.map(
                            (persona) => DropdownMenuItem<String?>(
                              value: persona.id,
                              child: Row(
                                children: [
                                  Text(
                                    persona.avatar,
                                    style: const TextStyle(fontSize: 16),
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      persona.name,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                        onChanged: (value) {
                          setState(() => _selectedPersonaId = value);
                        },
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: _isLoading ? null : _saveThread,
            child: _isLoading
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Text(widget.thread != null ? 'Update' : 'Create'),
          ),
        ],
      ),
    );
  }

  void _saveThread() {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    if (widget.thread != null) {
      // Update existing thread
      final updatedThread = widget.thread!.copyWith(
        title: _titleController.text.trim(),
        modelId: _selectedModelId,
        personaId: _selectedPersonaId,
      );
      context.read<ThreadsCubit>().updateThread(updatedThread);
    } else {
      // Create new thread
      context.read<ThreadsCubit>().createThread(
        spaceId: widget.space.id,
        title: _titleController.text.trim(),
        modelId: _selectedModelId,
        personaId: _selectedPersonaId,
      );
    }
  }
}

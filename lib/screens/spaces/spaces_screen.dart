import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../blocs/spaces/spaces_cubit.dart';
import '../../blocs/spaces/spaces_state.dart';
import '../../models/models.dart';
import '../../widgets/space_card.dart';
import '../../widgets/animated_grid.dart';
import '../../widgets/custom_snackbar.dart';
import '../../widgets/empty_state.dart';
import '../../widgets/shimmer_loading.dart';
import 'create_space_dialog.dart';
import 'space_detail_screen.dart';
import '../settings/settings_screen.dart';

class SpacesScreen extends StatelessWidget {
  const SpacesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Vaarta'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(builder: (context) => const SettingsScreen()),
              );
            },
          ),
        ],
      ),
      body: BlocConsumer<SpacesCubit, SpacesState>(
        listener: (context, state) {
          if (state is SpacesError) {
            CustomSnackBar.showError(
              context,
              message: 'Error: ${state.message}',
              actionLabel: 'Retry',
              onAction: () => context.read<SpacesCubit>().loadSpaces(),
            );
          } else if (state is SpaceCreated) {
            CustomSnackBar.showSuccess(
              context,
              message: 'Space "${state.space.title}" created successfully!',
            );
          }
        },
        builder: (context, state) {
          if (state is SpacesLoading) {
            return const SpacesLoadingSkeleton();
          }

          if (state is SpacesError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  Text(
                    'Error: ${state.message}',
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  FilledButton(
                    onPressed: () {
                      context.read<SpacesCubit>().loadSpaces();
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (state is SpacesLoaded) {
            if (state.spaces.isEmpty) {
              return EmptyState(
                icon: Icons.workspaces_outlined,
                title: 'No spaces yet',
                subtitle:
                    'Create your first workspace to get started with AI conversations',
                actionText: 'Create a space',
                onAction: () => _showCreateSpaceDialog(context),
              );
            }

            return StaggeredAnimatedGrid(
              padding: const EdgeInsets.all(16.0),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 1.2,
              ),
              itemCount: state.spaces.length,
              itemBuilder: (context, index) {
                final space = state.spaces[index];
                return SpaceCard(
                  space: space,
                  onTap: () => _navigateToSpace(context, space),
                  onEdit: () => _editSpace(context, space),
                  onDelete: () => _deleteSpace(context, space),
                );
              },
            );
          }

          return const SizedBox.shrink();
        },
      ),
      floatingActionButton: BlocBuilder<SpacesCubit, SpacesState>(
        builder: (context, state) {
          if (state is SpacesLoaded && state.spaces.isNotEmpty) {
            return FloatingActionButton.extended(
              onPressed: () => _showCreateSpaceDialog(context),
              icon: const Icon(Icons.add),
              label: const Text('New Space'),
              heroTag: 'create_space_fab',
            );
          } else if (state is SpaceCreating) {
            return FloatingActionButton(
              onPressed: null,
              heroTag: 'creating_space_fab',
              child: const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              ),
            );
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }

  void _showCreateSpaceDialog(BuildContext context, {Space? existingSpace}) {
    showDialog(
      context: context,
      builder: (context) => CreateSpaceDialog(space: existingSpace),
    );
  }

  void _navigateToSpace(BuildContext context, Space space) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => SpaceDetailScreen(space: space)),
    );
  }

  void _editSpace(BuildContext context, Space space) {
    _showCreateSpaceDialog(context, existingSpace: space);
  }

  void _deleteSpace(BuildContext context, Space space) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        icon: Icon(
          Icons.delete_outline,
          color: Theme.of(context).colorScheme.error,
          size: 32,
        ),
        title: const Text('Delete Space'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Are you sure you want to delete "${space.title}"?',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 8),
            Text(
              'This will also delete all conversations in this space. This action cannot be undone.',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              context.read<SpacesCubit>().deleteSpace(space.id);
              Navigator.of(context).pop();
              CustomSnackBar.showSuccess(
                context,
                message: 'Space "${space.title}" deleted successfully',
              );
            },
            style: FilledButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../blocs/personas/personas_cubit.dart';
import '../../blocs/personas/personas_state.dart';
import '../../models/models.dart';
import '../../widgets/widgets.dart';
import 'create_persona_screen.dart';

class PersonasScreen extends StatelessWidget {
  const PersonasScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI Personas'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _navigateToCreatePersona(context),
          ),
        ],
      ),
      body: BlocBuilder<PersonasCubit, PersonasState>(
        builder: (context, state) {
          if (state is PersonasLoading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is PersonasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading personas',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.message,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  FilledButton(
                    onPressed: () =>
                        context.read<PersonasCubit>().loadPersonas(),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          } else if (state is PersonasLoaded) {
            if (state.personas.isEmpty) {
              return EmptyState(
                icon: Icons.psychology,
                title: 'No Personas',
                subtitle: 'Create your first AI persona to get started',
                actionText: 'Create Persona',
                onAction: () => _navigateToCreatePersona(context),
              );
            }

            final builtInPersonas = state.personas
                .where((p) => p.isBuiltIn)
                .toList();
            final customPersonas = state.personas
                .where((p) => !p.isBuiltIn)
                .toList();

            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (builtInPersonas.isNotEmpty) ...[
                    Text(
                      'Built-in Personas',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ...builtInPersonas.map(
                      (persona) => _PersonaCard(
                        persona: persona,
                        isSelected: state.selectedPersona?.id == persona.id,
                        onTap: () => context
                            .read<PersonasCubit>()
                            .selectPersona(persona),
                        onEdit: null, // Built-in personas can't be edited
                      ),
                    ),
                  ],

                  if (customPersonas.isNotEmpty) ...[
                    if (builtInPersonas.isNotEmpty) const SizedBox(height: 32),
                    Text(
                      'Custom Personas',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ...customPersonas.map(
                      (persona) => _PersonaCard(
                        persona: persona,
                        isSelected: state.selectedPersona?.id == persona.id,
                        onTap: () => context
                            .read<PersonasCubit>()
                            .selectPersona(persona),
                        onEdit: () => _navigateToEditPersona(context, persona),
                      ),
                    ),
                  ],

                  const SizedBox(height: 80), // Space for FAB
                ],
              ),
            );
          }

          return const SizedBox.shrink();
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToCreatePersona(context),
        child: const Icon(Icons.add),
      ),
    );
  }

  void _navigateToCreatePersona(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const CreatePersonaScreen()),
    );
  }

  void _navigateToEditPersona(BuildContext context, AIPersona persona) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CreatePersonaScreen(persona: persona),
      ),
    );
  }
}

class _PersonaCard extends StatelessWidget {
  final AIPersona persona;
  final bool isSelected;
  final VoidCallback onTap;
  final VoidCallback? onEdit;

  const _PersonaCard({
    required this.persona,
    required this.isSelected,
    required this.onTap,
    this.onEdit,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: isSelected ? 4 : 1,
      color: isSelected ? Theme.of(context).colorScheme.primaryContainer : null,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Avatar
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  color: isSelected
                      ? Theme.of(
                          context,
                        ).colorScheme.primary.withValues(alpha: 0.1)
                      : Theme.of(context).colorScheme.surfaceVariant,
                  borderRadius: BorderRadius.circular(28),
                  border: isSelected
                      ? Border.all(
                          color: Theme.of(context).colorScheme.primary,
                          width: 2,
                        )
                      : null,
                ),
                child: Center(
                  child: Text(
                    persona.avatar,
                    style: const TextStyle(fontSize: 28),
                  ),
                ),
              ),

              const SizedBox(width: 16),

              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            persona.name,
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: isSelected
                                      ? Theme.of(
                                          context,
                                        ).colorScheme.onPrimaryContainer
                                      : null,
                                ),
                          ),
                        ),
                        if (persona.isBuiltIn)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Theme.of(
                                context,
                              ).colorScheme.secondaryContainer,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              'Built-in',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.onSecondaryContainer,
                                    fontSize: 10,
                                  ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      persona.description,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: isSelected
                            ? Theme.of(context).colorScheme.onPrimaryContainer
                                  .withValues(alpha: 0.8)
                            : Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (persona.specialties.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 6,
                        runSpacing: 4,
                        children: persona.specialties.take(3).map((specialty) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Theme.of(context)
                                  .colorScheme
                                  .tertiaryContainer
                                  .withValues(alpha: 0.6),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              specialty,
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.onTertiaryContainer,
                                    fontSize: 10,
                                  ),
                            ),
                          );
                        }).toList(),
                      ),
                    ],
                  ],
                ),
              ),

              // Actions
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (isSelected)
                    Icon(
                      Icons.check_circle,
                      color: Theme.of(context).colorScheme.primary,
                      size: 20,
                    ),
                  if (onEdit != null) ...[
                    const SizedBox(height: 8),
                    IconButton(
                      icon: const Icon(Icons.edit, size: 20),
                      onPressed: onEdit,
                      style: IconButton.styleFrom(
                        minimumSize: const Size(32, 32),
                        padding: EdgeInsets.zero,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

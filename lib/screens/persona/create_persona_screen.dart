import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../blocs/personas/personas_cubit.dart';
import '../../blocs/personas/personas_state.dart';
import '../../models/models.dart';

class CreatePersonaScreen extends StatefulWidget {
  final AIPersona? persona; // For editing existing persona

  const CreatePersonaScreen({super.key, this.persona});

  @override
  State<CreatePersonaScreen> createState() => _CreatePersonaScreenState();
}

class _CreatePersonaScreenState extends State<CreatePersonaScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _systemPromptController = TextEditingController();
  final _specialtiesController = TextEditingController();

  String _selectedAvatar = '🤖';
  bool _isLoading = false;

  final List<String> _availableAvatars = [
    '🤖',
    '👨‍💻',
    '👩‍💻',
    '🧠',
    '💡',
    '🎯',
    '📚',
    '🔬',
    '🎨',
    '✍️',
    '🏥',
    '⚖️',
    '💼',
    '🎓',
    '🔧',
    '🍳',
    '🎵',
    '📊',
    '🌟',
    '🚀',
    '💰',
    '🏋️',
    '🧘',
    '🌱',
    '🎭',
    '📝',
    '🔍',
    '💻',
    '🎪',
    '🏆',
  ];

  @override
  void initState() {
    super.initState();
    if (widget.persona != null) {
      _nameController.text = widget.persona!.name;
      _descriptionController.text = widget.persona!.description;
      _systemPromptController.text = widget.persona!.systemPrompt;
      _specialtiesController.text = widget.persona!.specialties.join(', ');
      _selectedAvatar = widget.persona!.avatar;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _systemPromptController.dispose();
    _specialtiesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.persona != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? 'Edit Persona' : 'Create Persona'),
        actions: [
          if (isEditing && !widget.persona!.isBuiltIn)
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: _showDeleteConfirmation,
            ),
        ],
      ),
      body: BlocListener<PersonasCubit, PersonasState>(
        listener: (context, state) {
          if (state is PersonasError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Theme.of(context).colorScheme.error,
              ),
            );
            setState(() => _isLoading = false);
          } else if (state is PersonasLoaded) {
            if (_isLoading) {
              Navigator.of(context).pop();
            }
          }
        },
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Avatar Selection
                Text(
                  'Avatar',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 12),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Theme.of(
                        context,
                      ).colorScheme.outline.withValues(alpha: 0.3),
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      // Selected avatar display
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primaryContainer,
                          borderRadius: BorderRadius.circular(40),
                        ),
                        child: Center(
                          child: Text(
                            _selectedAvatar,
                            style: const TextStyle(fontSize: 40),
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      // Avatar grid
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: _availableAvatars.map((avatar) {
                          final isSelected = avatar == _selectedAvatar;
                          return GestureDetector(
                            onTap: () =>
                                setState(() => _selectedAvatar = avatar),
                            child: Container(
                              width: 48,
                              height: 48,
                              decoration: BoxDecoration(
                                color: isSelected
                                    ? Theme.of(context).colorScheme.primary
                                    : Theme.of(
                                        context,
                                      ).colorScheme.surfaceVariant,
                                borderRadius: BorderRadius.circular(24),
                                border: isSelected
                                    ? Border.all(
                                        color: Theme.of(
                                          context,
                                        ).colorScheme.primary,
                                        width: 2,
                                      )
                                    : null,
                              ),
                              child: Center(
                                child: Text(
                                  avatar,
                                  style: const TextStyle(fontSize: 24),
                                ),
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // Name Field
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'Name',
                    hintText: 'Enter persona name',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter a name';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // Description Field
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description',
                    hintText: 'Brief description of the persona',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter a description';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // Specialties Field
                TextFormField(
                  controller: _specialtiesController,
                  decoration: const InputDecoration(
                    labelText: 'Specialties',
                    hintText:
                        'Comma-separated specialties (e.g., coding, writing, analysis)',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),

                const SizedBox(height: 16),

                // System Prompt Field
                TextFormField(
                  controller: _systemPromptController,
                  decoration: const InputDecoration(
                    labelText: 'System Prompt',
                    hintText:
                        'Instructions that define how the AI should behave',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 6,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter a system prompt';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 32),

                // Save Button
                SizedBox(
                  width: double.infinity,
                  child: FilledButton(
                    onPressed: _isLoading ? null : _savePersona,
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : Text(isEditing ? 'Update Persona' : 'Create Persona'),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _savePersona() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    final specialties = _specialtiesController.text
        .split(',')
        .map((s) => s.trim())
        .where((s) => s.isNotEmpty)
        .toList();

    if (widget.persona != null) {
      // Update existing persona
      final persona = AIPersona(
        id: widget.persona!.id,
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        avatar: _selectedAvatar,
        systemPrompt: _systemPromptController.text.trim(),
        specialties: specialties,
        isBuiltIn: false,
      );
      context.read<PersonasCubit>().updatePersona(persona);
    } else {
      // Create new persona
      context.read<PersonasCubit>().createPersona(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        avatar: _selectedAvatar,
        systemPrompt: _systemPromptController.text.trim(),
        specialties: specialties,
      );
    }
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Persona'),
        content: Text(
          'Are you sure you want to delete "${widget.persona!.name}"?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<PersonasCubit>().deletePersona(widget.persona!.id);
              Navigator.pop(context);
            },
            style: FilledButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

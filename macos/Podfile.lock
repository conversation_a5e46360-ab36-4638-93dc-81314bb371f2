PODS:
  - CwlCatchException (2.2.1):
    - CwlCatchExceptionSupport (~> 2.2.1)
  - CwlCatchExceptionSupport (2.2.1)
  - flutter_secure_storage_macos (6.1.3):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - speech_to_text (7.2.0):
    - CwlCatchException
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - flutter_secure_storage_macos (from `Flutter/ephemeral/.symlinks/plugins/flutter_secure_storage_macos/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - speech_to_text (from `Flutter/ephemeral/.symlinks/plugins/speech_to_text/darwin`)

SPEC REPOS:
  trunk:
    - CwlCatchException
    - CwlCatchExceptionSupport

EXTERNAL SOURCES:
  flutter_secure_storage_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_secure_storage_macos/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  speech_to_text:
    :path: Flutter/ephemeral/.symlinks/plugins/speech_to_text/darwin

SPEC CHECKSUMS:
  CwlCatchException: 7acc161b299a6de7f0a46a6ed741eae2c8b4d75a
  CwlCatchExceptionSupport: 54ccab8d8c78907b57f99717fb19d4cc3bce02dc
  flutter_secure_storage_macos: 7f45e30f838cf2659862a4e4e3ee1c347c2b3b54
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  speech_to_text: 3b313d98516d3d0406cea424782ec25470c59d19

PODFILE CHECKSUM: 346bfb2deb41d4a6ebd6f6799f92188bde2d246f

COCOAPODS: 1.16.2
